import cv2
import numpy as np
from ultralytics import YOL<PERSON>
import torch
from ultralytics.nn.tasks import DetectionModel
from torch.nn.modules.container import Sequential
from ultralytics.nn.modules import Conv

# 添加安全全局变量
torch.serialization.add_safe_globals([DetectionModel, Sequential, Conv])

class PeopleDetector:
    def __init__(self, model_path: str):
        # 保存原始的 torch.load 函数
        original_torch_load = torch.load

        # 定义新的加载函数
        def custom_load(*args, **kwargs):
            kwargs['weights_only'] = False
            return original_torch_load(*args, **kwargs)

        # 替换 torch.load 函数
        torch.load = custom_load
        
        self.model = YOLO(model_path)
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {self.device}")
        
    def detect(self, frame):
        # 将BGR转换为RGB
        rgb_img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        # 使用YOLO的追踪模式
        results = self.model.track(rgb_img, persist=True, verbose=False)
        return results[0] if results else None

class PeopleCounter:
    def __init__(self, line_points):
        self.line_points = line_points
        # 上面的区域
        self.region1 = np.array([
            (line_points[0][0], line_points[0][1] - 20),
            (line_points[0][0], line_points[0][1]),
            (line_points[1][0], line_points[1][1]),
            (line_points[1][0], line_points[1][1] - 20)
        ]).reshape((-1,1,2))
        
        # 下面的区域
        self.region2 = np.array([
            (line_points[0][0], line_points[0][1]),
            (line_points[0][0], line_points[0][1] + 20),
            (line_points[1][0], line_points[1][1] + 20),
            (line_points[1][0], line_points[1][1])
        ]).reshape((-1,1,2))
        
        # 进入上面区域的行人id
        self.in_region1 = set()
        # 进入下面区域的行人id
        self.in_region2 = set()
        # 进入的行人id
        self.first_in = set()
        # 出去的行人id
        self.first_out = set()
        
        # 已经计数过的行人id
        self.counted_in = set()
        self.counted_out = set()
        
        # 当前帧检测到的id
        self.current_ids = set()
        
    def update(self, results, frame):
        if results is None:
            return len(self.first_in), len(self.first_out)
            
        # 清空当前帧的ID列表
        self.current_ids = set()
            
        for i in range(len(results.boxes)):
            # 获取框的坐标
            x1, y1, x2, y2 = results.boxes.xyxy[i]
            # 获取置信度
            score = results.boxes.conf[i]
            # 获取类别
            cls = results.boxes.cls[i]
            # 获取ID
            ids = results.boxes.id[i]
            
            # 转换为整数
            x1, y1, x2, y2, score, cls, ids = int(x1), int(y1), int(x2), int(y2), float(score), int(cls), int(ids)
            
            # 设置阈值，只有高于这个阈值才会被标记
            if score < 0.2:
                continue
            # 只处理行人（类别0）
            if cls != 0:
                continue
                
            # 添加到当前帧检测到的ID列表中
            self.current_ids.add(ids)
                
            # 绘制边框
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # 绘制ID标签
            cv2.putText(frame, f'ID: {ids}', (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
            # 计算行人中心位置
            cx = int((x1 + x2) / 2)
            cy = int((y1 + y2) / 2)
            
            # 绘制中心点
            cv2.circle(frame, (cx, cy), 4, (0, 255, 255), -1)
            
            # 判断是否在区域1内
            inside_region1 = cv2.pointPolygonTest(self.region1, (cx, cy), False)
            if inside_region1 > 0:
                self.in_region1.add(ids)
                
            # 判断是否在区域2内
            inside_region2 = cv2.pointPolygonTest(self.region2, (cx, cy), False)
            if inside_region2 > 0:
                self.in_region2.add(ids)
            
            # 从区域1移动到区域2，且还未计数
            if ids in self.in_region1 and ids in self.in_region2 and ids not in self.counted_in:
                self.first_in.add(ids)
                self.counted_in.add(ids)
                # 显示越线标识
                cv2.line(frame, self.line_points[0], self.line_points[1], (255, 255, 255), 3)
                
            # 从区域2移动到区域1，且还未计数
            if ids in self.in_region2 and ids in self.in_region1 and ids not in self.counted_out:
                self.first_out.add(ids)
                self.counted_out.add(ids)
                # 显示越线标识
                cv2.line(frame, self.line_points[0], self.line_points[1], (255, 255, 255), 3)
                
        # 清理不在当前帧中的ID
        self.in_region1 = self.in_region1.intersection(self.current_ids)
        self.in_region2 = self.in_region2.intersection(self.current_ids)
                
        return len(self.first_in), len(self.first_out)
        
    def draw_results(self, frame):
        # 绘制计数线
        cv2.line(frame, self.line_points[0], self.line_points[1], (0, 255, 0), 2)
        
        # 可选：绘制区域 - 注释掉这两行，不再显示区域边界
        # cv2.polylines(frame, [self.region1], True, (255, 0, 0), 2)
        # cv2.polylines(frame, [self.region2], True, (255, 0, 255), 2)
        
        # 设置显示区域背景色
        frame[0:40, 0:120] = (102, 0, 153)
        frame[0:40, 510:640] = (102, 0, 153)
        
        # 显示计数结果
        font = cv2.FONT_HERSHEY_DUPLEX
        cv2.putText(frame, f'IN: {len(self.first_in)}', (0, 30), font, 1, (255, 255, 255), 1)
        cv2.putText(frame, f'OUT: {len(self.first_out)}', (510, 30), font, 1, (255, 255, 255), 1)
        
        return frame 