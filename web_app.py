from flask import Flask, render_template, Response, jsonify, request, redirect, url_for, session
from flask_cors import CORS
import cv2
import numpy as np
import threading
import time
import logging
import datetime
import os
from functools import wraps
from werkzeug.utils import secure_filename
from detector import PeopleDetector, PeopleCounter
from database import Database

# 配置上传文件保存位置
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'mp4', 'avi', 'mov', 'mkv', 'webm'}

# 确保上传目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 限制上传文件大小为500MB
app.secret_key = 'your_secret_key_here'  # 用于session加密，生产环境应使用随机生成的密钥

# 全局变量
detector = None
counter = None
camera = None
frame = None
in_count = 0
out_count = 0
is_running = False
video_path = 'uploads/video.mp4'  # 默认视频文件路径
db = None
last_save_time = time.time()  # 上次保存到数据库的时间
save_interval = 5  # 每5秒保存一次数据

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def init_detector():
    global detector, counter, db, video_path
    detector = PeopleDetector('yolov5n.pt')
    
    # 获取视频尺寸
    cap = cv2.VideoCapture(video_path)
    if cap.isOpened():
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()
    else:
        width, height = 640, 360  # 默认尺寸
    
    # 根据视频尺寸动态设置计数线
    line_points = ((0, int(height * 0.36)), (width, int(height * 0.78)))
    counter = PeopleCounter(line_points)
    
    # 初始化数据库
    try:
        db = Database()
        logger.info("数据库初始化成功")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        db = None

def process_frame_with_delay():
    """带2秒延迟的处理函数"""
    global frame

    # 显示准备中的提示，等待2秒
    logger.info("检测启动中，等待2秒...")

    # 在等待期间显示准备提示
    for i in range(20):  # 2秒，每0.1秒更新一次
        if not is_running:  # 如果在等待期间被停止，直接返回
            return

        # 创建准备中的提示帧
        prepare_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
        cv2.putText(prepare_frame, f"检测启动中... {2 - i*0.1:.1f}s", (180, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 100, 0), 2)
        frame = prepare_frame.copy()
        time.sleep(0.1)

    # 2秒后开始正常处理
    if is_running:
        logger.info("开始视频处理...")
        process_frame()

def process_frame():
    global camera, frame, detector, counter, in_count, out_count, is_running, db, last_save_time
    
    while is_running:
        try:
            if camera is None:
                logger.info("视频未初始化，尝试重新初始化...")
                # 确保视频文件存在
                if not os.path.exists(video_path):
                    logger.error(f"视频文件不存在: {video_path}")
                    error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
                    cv2.putText(error_frame, f"视频文件不存在: {os.path.basename(video_path)}", (50, 240),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    frame = error_frame.copy()
                    time.sleep(1)
                    continue
                
                # 尝试打开视频
                camera = cv2.VideoCapture(video_path)
                if not camera.isOpened():
                    logger.error(f"无法打开视频文件: {video_path}")
                    # 生成错误帧
                    error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
                    cv2.putText(error_frame, f"无法打开视频: {os.path.basename(video_path)}", (50, 240),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    frame = error_frame.copy()
                    time.sleep(1)
                    continue
                
                logger.info(f"成功打开视频: {video_path}")
            
            # 读取视频帧
            success, current_frame = camera.read()
            if not success:
                logger.info("视频播放结束，重新开始播放")
                camera.set(cv2.CAP_PROP_POS_FRAMES, 0)  # 重置到视频开始
                success, current_frame = camera.read()  # 再次尝试读取
                if not success:
                    # 如果仍然无法读取，创建一个错误帧
                    error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
                    cv2.putText(error_frame, "无法读取视频帧，请检查视频文件", (50, 240),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                    frame = error_frame.copy()
                    # 释放摄像头资源，下次循环会重新尝试打开
                    camera.release()
                    camera = None
                    time.sleep(0.5)
                    continue

            # 确保帧数据类型正确
            if current_frame is not None and current_frame.dtype != np.uint8:
                current_frame = current_frame.astype(np.uint8)
            
            # 检测行人
            results = detector.detect(current_frame)
            
            # 更新计数
            in_count, out_count = counter.update(results, current_frame)
            
            # 绘制结果
            result_frame = counter.draw_results(current_frame.copy())
            
            # 更新全局frame (确保复制以避免引用问题)
            frame = result_frame.copy()
            
            # 每隔一段时间保存计数数据到数据库
            current_time = time.time()
            if db is not None and (current_time - last_save_time) >= save_interval:
                try:
                    db.save_count(in_count, out_count, video_path)
                    last_save_time = current_time
                    logger.info(f"数据已保存到数据库: in={in_count}, out={out_count}")
                except Exception as e:
                    logger.error(f"保存数据到数据库失败: {e}")
            
            # 控制处理帧率，根据系统性能可能需要调整
            time.sleep(0.01)
        except Exception as e:
            logger.error(f"处理视频帧出错: {e}")
            # 创建错误提示帧
            try:
                error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
                cv2.putText(error_frame, f"处理视频帧出错: {str(e)[:30]}", (50, 240),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                frame = error_frame.copy()
            except Exception as inner_e:
                logger.error(f"创建错误帧失败: {inner_e}")
            time.sleep(0.5)  # 错误后稍等一下再继续

# 登录和权限管理
@app.route('/login')
def login():
    return render_template('login.html')

@app.route('/api/login', methods=['POST'])
def api_login():
    data = request.json
    username = data.get('username')
    password = data.get('password')
    role = data.get('role')
    
    # 检查请求数据是否完整
    if not all([username, password, role]):
        return jsonify({'status': 'error', 'message': '请提供完整的登录信息'}), 400
    
    # 简化的用户验证逻辑，实际应用中应使用数据库和加密密码
    valid_credentials = {
        'admin': {'password': 'admin123', 'role': 'admin'},
        'operator': {'password': 'op123', 'role': 'operator'},
        'guest': {'password': 'guest123', 'role': 'viewer'}
    }
    
    # 检查用户名是否存在
    if username not in valid_credentials:
        return jsonify({'status': 'error', 'message': '用户名不存在'}), 401
    
    # 检查密码是否正确
    if valid_credentials[username]['password'] != password:
        return jsonify({'status': 'error', 'message': '密码错误'}), 401
    
    # 验证角色是否匹配
    if valid_credentials[username]['role'] != role:
        return jsonify({'status': 'error', 'message': '所选角色与用户不匹配'}), 401
    
    # 验证通过，设置session
    session['logged_in'] = True
    session['username'] = username
    session['role'] = role
    
    # 登录成功，返回成功信息
    return jsonify({'status': 'success', 'role': role})

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

# 检查登录状态的装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# 更新路由，添加登录验证
@app.route('/')
@login_required
def index():
    return render_template('index.html', user_role=session.get('role', 'viewer'), username=session.get('username', ''))

@app.route('/statistics')
@login_required
def statistics():
    return render_template('statistics.html', user_role=session.get('role', 'viewer'), username=session.get('username', ''))

@app.route('/video_feed')
def video_feed():
    """处理视频流"""
    global is_running

    try:
        def gen_frames():
            global frame, is_running, camera
            last_frame_time = time.time()
            frame_interval = 0.04  # 约25fps

            while True:
                try:
                    current_time = time.time()
                    # 控制帧率
                    if current_time - last_frame_time < frame_interval:
                        time.sleep(0.005)
                        continue
                        
                    last_frame_time = current_time
                    
                    # 如果系统未运行，显示等待提示
                    if not is_running:
                        blank_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255  # 白色背景
                        cv2.putText(blank_frame, "系统就绪，请点击'开始检测'按钮", (50, 240),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
                        ret, buffer = cv2.imencode('.jpg', blank_frame)
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                  b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                        time.sleep(0.5)  # 减少CPU使用
                        continue
                        
                    # 确保frame不为None
                    if frame is not None:
                        # 复制一份避免竞态条件
                        try:
                            current_frame = frame.copy()

                            # 确保帧数据类型正确
                            if current_frame.dtype != np.uint8:
                                current_frame = current_frame.astype(np.uint8)

                            # 确保帧尺寸合理
                            if current_frame.shape[0] == 0 or current_frame.shape[1] == 0:
                                raise ValueError("帧尺寸无效")

                            # 降低质量以提高传输速度
                            quality_param = [int(cv2.IMWRITE_JPEG_QUALITY), 85]
                            ret, buffer = cv2.imencode('.jpg', current_frame, quality_param)
                            if ret and buffer is not None and len(buffer) > 0:
                                frame_bytes = buffer.tobytes()
                                yield (b'--frame\r\n'
                                      b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                            else:
                                # 编码失败，生成默认帧
                                default_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
                                cv2.putText(default_frame, "视频编码失败", (200, 240),
                                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
                                ret, buffer = cv2.imencode('.jpg', default_frame)
                                if ret:
                                    frame_bytes = buffer.tobytes()
                                    yield (b'--frame\r\n'
                                          b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                                time.sleep(0.1)
                        except Exception as frame_error:
                            logging.error(f"处理视频帧异常: {frame_error}")
                            # 生成一个错误提示帧
                            error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
                            cv2.putText(error_frame, f"处理帧异常: {str(frame_error)[:30]}", (50, 240),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                            ret, buffer = cv2.imencode('.jpg', error_frame)
                            if ret:
                                frame_bytes = buffer.tobytes()
                                yield (b'--frame\r\n'
                                      b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                            time.sleep(0.1)
                    else:
                        # 如果frame为None，根据运行状态显示不同提示
                        if is_running:
                            # 运行中但没有frame，显示准备视频提示
                            blank_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255  # 白色背景
                            cv2.putText(blank_frame, "正在准备视频...", (150, 240),
                                      cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
                        else:
                            # 未运行，显示初始提示
                            blank_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255  # 白色背景
                            cv2.putText(blank_frame, "系统就绪，请点击'开始检测'按钮", (50, 240),
                                      cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)

                        ret, buffer = cv2.imencode('.jpg', blank_frame)
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                  b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')

                        # 等待视频输入
                        time.sleep(0.1)
                except Exception as e:
                    logging.error(f"视频流生成错误: {e}")
                    # 发生错误时，返回一个错误提示帧
                    try:
                        error_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255  # 白色背景
                        cv2.putText(error_frame, f"视频流错误: {str(e)[:30]}", (50, 240),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
                        ret, buffer = cv2.imencode('.jpg', error_frame)
                        if ret:
                            frame_bytes = buffer.tobytes()
                            yield (b'--frame\r\n'
                                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                    except Exception as inner_e:
                        logging.error(f"生成错误帧失败: {inner_e}")
                    
                    time.sleep(0.5)
        
        # 设置响应头，添加缓存控制头，防止浏览器缓存流
        response = Response(
            gen_frames(),
            mimetype='multipart/x-mixed-replace; boundary=frame'
        )
        response.headers['Cache-Control'] = 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0, max-age=0'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
        return response
    except Exception as e:
        logger.error(f"视频流路由出错: {e}")
        # 返回错误图像
        blank_frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
        cv2.putText(blank_frame, f"视频流初始化错误: {str(e)[:30]}", (50, 240),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        ret, buffer = cv2.imencode('.jpg', blank_frame)
        if ret:
            frame_bytes = buffer.tobytes()
            return Response(
                b'--frame\r\n'
                b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n',
                mimetype='multipart/x-mixed-replace; boundary=frame'
            )

@app.route('/counts')
def get_counts():
    return jsonify({
        'in_count': in_count,
        'out_count': out_count
    })

@app.route('/start')
def start_detection():
    global is_running, camera, frame, in_count, out_count
    
    if not is_running:
        try:
            # 重置计数器
            in_count = 0
            out_count = 0
            
            # 确保之前的摄像头已释放
            if camera is not None:
                camera.release()
                camera = None
            
            # 等待一小段时间确保摄像头资源被释放
            time.sleep(0.2)
                
            # 初始化视频
            camera = cv2.VideoCapture(video_path)
            if not camera.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return jsonify({'status': 'error', 'message': f'无法打开视频文件: {video_path}'})
                
            # 读取一帧来确认视频能正常播放
            ret, test_frame = camera.read()
            if not ret:
                logger.error("无法从视频读取帧")
                return jsonify({'status': 'error', 'message': '无法从视频读取帧'})
            
            # 设置视频回到开始
            camera.set(cv2.CAP_PROP_POS_FRAMES, 0)
            
            # 确保检测器和计数器已正确初始化
            if detector is None or counter is None:
                init_detector()
            
            is_running = True

            # 启动处理线程（带2秒延迟）
            processing_thread = threading.Thread(target=process_frame_with_delay, daemon=True)
            processing_thread.start()

            logger.info("行人检测已启动")
            return jsonify({'status': 'success'})
        except Exception as e:
            logger.error(f"启动检测失败: {e}")
            if camera is not None:
                camera.release()
                camera = None
            return jsonify({'status': 'error', 'message': f'启动失败: {e}'})
    return jsonify({'status': 'already_running'})

@app.route('/stop')
def stop_detection():
    global is_running, camera, frame
    is_running = False
    if camera is not None:
        camera.release()
        camera = None

    # 停止后重置为初始提示图片
    frame = None

    logger.info("行人检测已停止")
    return jsonify({'status': 'success'})

# 数据统计相关API
@app.route('/hourly_stats')
def get_hourly_stats():
    if db is None:
        return jsonify([])
    
    date_str = request.args.get('date')
    if date_str:
        try:
            date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            date = None
    else:
        date = None
    
    stats = db.get_hourly_statistics(date)
    
    # 格式化为前端需要的格式
    result = []
    for hour, in_count, out_count in stats:
        result.append({
            'hour': hour,
            'in_count': in_count,
            'out_count': out_count
        })
    
    return jsonify(result)

@app.route('/daily_stats')
def get_daily_stats():
    if db is None:
        return jsonify([])
    
    days = request.args.get('days', default=7, type=int)
    
    try:
        stats = db.get_daily_statistics(days)
        
        # 格式化为前端需要的格式
        result = []
        for date, in_count, out_count in stats:
            result.append({
                'date': date.strftime('%Y-%m-%d') if hasattr(date, 'strftime') else str(date),
                'in_count': in_count if in_count is not None else 0,
                'out_count': out_count if out_count is not None else 0
            })
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取每日统计数据失败: {e}")
        # 返回空数据而不是错误，前端可以继续运行
        return jsonify([])

@app.route('/api/summary')
def get_summary():
    if db is None:
        return jsonify({'total_in': 0, 'total_out': 0})
    
    try:
        # 获取总计数
        db.cursor.execute(
            "SELECT SUM(in_count), SUM(out_count) FROM statistics"
        )
        result = db.cursor.fetchone()
        
        if result and result[0] is not None:
            total_in = int(result[0])
            total_out = int(result[1])
        else:
            total_in = 0
            total_out = 0
            
        return jsonify({
            'total_in': total_in,
            'total_out': total_out
        })
    except Exception as e:
        logger.error(f"获取统计摘要失败: {e}")
        return jsonify({'total_in': 0, 'total_out': 0})

@app.route('/api/hourly_stats')
def api_hourly_stats():
    return get_hourly_stats()

@app.route('/api/daily_stats')
def api_daily_stats():
    return get_daily_stats()

@app.route('/api/weekly_stats')
def api_weekly_stats():
    if db is None:
        return jsonify([])
    
    try:
        # 获取每周统计
        db.cursor.execute(
            """
            SELECT 
                YEARWEEK(date, 1) as yearweek, 
                WEEK(date, 1) as week,
                SUM(in_count) as in_count, 
                SUM(out_count) as out_count 
            FROM 
                statistics 
            GROUP BY 
                YEARWEEK(date, 1)
            ORDER BY 
                YEARWEEK(date, 1) DESC
            LIMIT 10
            """
        )
        
        results = db.cursor.fetchall()
        
        # 格式化为前端需要的格式
        result = []
        for yearweek, week, in_count, out_count in results:
            result.append({
                'yearweek': yearweek,
                'week': week,
                'in_count': in_count,
                'out_count': out_count
            })
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取每周统计失败: {e}")
        return jsonify([])

@app.route('/upload_video', methods=['POST'])
def upload_video():
    global video_path, is_running, in_count, out_count, camera, detector, counter
    
    # 如果正在运行，先停止
    if is_running:
        is_running = False
        if camera is not None:
            camera.release()
        time.sleep(0.5)  # 等待资源释放
    
    # 检查是否有文件
    if 'video_file' not in request.files:
        return jsonify({'status': 'error', 'message': '没有选择文件'})
    
    file = request.files['video_file']
    
    # 如果用户没有选择文件，浏览器会提交一个空文件而不带文件名
    if file.filename == '':
        return jsonify({'status': 'error', 'message': '没有选择文件'})
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        video_path = filepath
        logger.info(f"已上传新视频: {filepath}")
        
        # 重置计数器
        in_count = 0
        out_count = 0
        
        # 重新初始化检测器以更新计数线
        init_detector()
        
        return jsonify({'status': 'success', 'message': f'文件 {filename} 上传成功', 'path': video_path})
    
    return jsonify({'status': 'error', 'message': '不支持的文件类型'})

@app.route('/videos')
def list_videos():
    """获取可用的视频文件列表"""
    videos = []
    
    # 添加默认视频
    if os.path.exists('video.mp4'):
        videos.append({'name': '默认视频', 'path': 'video.mp4'})
    
    # 获取上传的视频
    if os.path.exists(UPLOAD_FOLDER):
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                videos.append({'name': filename, 'path': filepath})
    
    return jsonify(videos)

@app.route('/select_video', methods=['POST'])
def select_video():
    global video_path, is_running, in_count, out_count, camera, detector, counter
    
    # 如果正在运行，先停止
    if is_running:
        is_running = False
        if camera is not None:
            camera.release()
            camera = None
        time.sleep(0.5)  # 等待资源释放
    
    data = request.json
    selected_path = data.get('path')
    
    if not selected_path or not os.path.exists(selected_path):
        return jsonify({'status': 'error', 'message': f'视频文件 {selected_path} 不存在'})
    
    # 更新视频路径
    video_path = selected_path
    logger.info(f"已切换视频源: {video_path}")
    
    # 重置计数器
    in_count = 0
    out_count = 0
    
    # 重新初始化检测器以更新计数线
    init_detector()
    
    return jsonify({'status': 'success', 'message': '视频切换成功'})

@app.route('/set_line', methods=['POST'])
def set_line():
    global counter, detector, is_running, camera, in_count, out_count
    
    try:
        data = request.json
        start_point = data.get('start_point')
        end_point = data.get('end_point')
        
        if not start_point or not end_point or len(start_point) != 2 or len(end_point) != 2:
            return jsonify({'status': 'error', 'message': '无效的线段坐标'})
        
        # 确保坐标是整数元组
        start_point = (int(start_point[0]), int(start_point[1]))
        end_point = (int(end_point[0]), int(end_point[1]))
        
        # 检查是否需要停止当前检测
        restart_detection = False
        if is_running:
            is_running = False
            restart_detection = True
            if camera is not None:
                camera.release()
                camera = None
            time.sleep(0.5)  # 等待资源释放
        
        # 重置计数器
        in_count = 0
        out_count = 0
        
        # 创建新的检测器实例
        if detector is None:
            detector = PeopleDetector('yolov5n.pt')
        
        # 使用自定义线段创建计数器
        line_points = (start_point, end_point)
        counter = PeopleCounter(line_points)
        
        logger.info(f"检测线已设置为: {line_points}")
        
        # 如果之前在检测，则重新启动检测
        if restart_detection:
            # 初始化视频
            camera = cv2.VideoCapture(video_path)
            if not camera.isOpened():
                logger.error(f"无法打开视频文件: {video_path}")
                return jsonify({'status': 'error', 'message': f'无法打开视频文件: {video_path}'})
            
            is_running = True
            # 启动处理线程
            processing_thread = threading.Thread(target=process_frame, daemon=True)
            processing_thread.start()
        
        return jsonify({'status': 'success', 'message': '检测线设置成功'})
    except Exception as e:
        logger.error(f"设置检测线失败: {e}")
        return jsonify({'status': 'error', 'message': f'设置失败: {e}'})

@app.route('/video_dimensions')
def get_video_dimensions():
    global video_path
    
    try:
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()
            return jsonify({'width': width, 'height': height})
        else:
            # 如果无法打开视频，返回默认尺寸
            return jsonify({'width': 640, 'height': 360})
    except Exception as e:
        logger.error(f"获取视频尺寸失败: {e}")
        return jsonify({'width': 640, 'height': 360})

if __name__ == '__main__':
    init_detector()
    app.run(host='0.0.0.0', port=5000, debug=True) 