"""
数据库初始化脚本
用于创建 people_count 数据库和相关表结构
"""

import mysql.connector
import logging
import sys
import getpass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def create_database():
    """创建数据库和表结构"""
    # 获取用户输入的数据库信息
    host = input("请输入MySQL主机地址 [localhost]: ") or "localhost"
    user = input("请输入MySQL用户名 [root]: ") or "root"
    password = getpass.getpass("请输入MySQL密码: ")
    
    try:
        # 首先尝试连接MySQL服务器
        logging.info(f"尝试连接到MySQL服务器 {host}")
        conn = mysql.connector.connect(
            host=host,
            user=user,
            password=password
        )
        cursor = conn.cursor()
        
        # 创建数据库
        logging.info("创建数据库 people_count")
        cursor.execute("CREATE DATABASE IF NOT EXISTS people_count")
        
        # 切换到新创建的数据库
        cursor.execute("USE people_count")
        
        # 创建计数记录表
        logging.info("创建表 count_records")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS count_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            in_count INT NOT NULL,
            out_count INT NOT NULL,
            total_count INT NOT NULL,
            video_source VARCHAR(255)
        )
        """)
        
        # 创建统计数据表
        logging.info("创建表 statistics")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS statistics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            date DATE NOT NULL,
            hour INT NOT NULL,
            in_count INT NOT NULL,
            out_count INT NOT NULL,
            UNIQUE KEY date_hour (date, hour)
        )
        """)
        
        conn.commit()
        logging.info("数据库初始化成功！")
        
        # 更新database.py中的连接信息
        update_connection_info(host, user, password)
        
        return True
    except mysql.connector.Error as err:
        logging.error(f"数据库初始化失败: {err}")
        return False
    finally:
        if 'conn' in locals() and conn.is_connected():
            cursor.close()
            conn.close()

def update_connection_info(host, user, password):
    """更新database.py中的连接信息"""
    try:
        with open('database.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新连接信息
        content = content.replace('host="localhost"', f'host="{host}"')
        content = content.replace('user="root"', f'user="{user}"')
        content = content.replace('password=""', f'password="{password}"')
        
        with open('database.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        logging.info("已更新数据库连接信息")
    except Exception as e:
        logging.error(f"更新数据库连接信息失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("行人计数系统 - 数据库初始化工具")
    print("=" * 50)
    print("此工具将帮助你创建并配置MySQL数据库")
    print()
    
    if create_database():
        print()
        print("数据库初始化成功！现在你可以运行程序了:")
        print("python web_app.py")
    else:
        print()
        print("数据库初始化失败，请检查错误信息并重试")
        sys.exit(1) 