import os
import yaml
from ultralytics import YOL<PERSON>
from pathlib import Path

def train_model(data_yaml_path, model_size='n', epochs=100, batch_size=16, img_size=640):
    """
    训练YOLOv5模型
    Args:
        data_yaml_path: 数据配置文件路径
        model_size: 模型大小 ('n', 's', 'm', 'l', 'x')
        epochs: 训练轮数
        batch_size: 批次大小
        img_size: 图像大小
    """
    # 创建模型
    model = YOLO(f'yolov5{model_size}.pt')
    
    # 训练参数
    params = {
        'data': data_yaml_path,
        'epochs': epochs,
        'batch': batch_size,
        'imgsz': img_size,
        'patience': 50,  # 早停耐心值
        'device': 0,     # GPU设备
        'workers': 8,    # 数据加载线程数
        'project': 'runs/train',  # 保存结果的项目名称
        'name': f'exp_{model_size}',  # 实验名称
        'exist_ok': True,  # 允许覆盖已存在的实验文件夹
        'pretrained': True,  # 使用预训练权重
        'optimizer': 'Adam',  # 优化器
        'lr0': 0.001,  # 初始学习率
        'lrf': 0.01,   # 最终学习率
        'momentum': 0.937,  # SGD动量
        'weight_decay': 0.0005,  # 权重衰减
        'warmup_epochs': 3,  # 预热轮数
        'warmup_momentum': 0.8,  # 预热动量
        'warmup_bias_lr': 0.1,  # 预热偏置学习率
        'box': 7.5,  # 框损失增益
        'cls': 0.5,  # 分类损失增益
        'cls_pw': 1.0,  # 分类正样本权重
        'obj': 1.0,  # 目标损失增益
        'obj_pw': 1.0,  # 目标正样本权重
        'iou_t': 0.20,  # IoU阈值
        'anchor_t': 4.0,  # 锚框阈值
        'fl_gamma': 0.0,  # 焦点损失gamma
        'hsv_h': 0.015,  # HSV-Hue增强
        'hsv_s': 0.7,    # HSV-Saturation增强
        'hsv_v': 0.4,    # HSV-Value增强
        'degrees': 0.0,  # 旋转角度
        'translate': 0.1,  # 平移
        'scale': 0.5,    # 缩放
        'shear': 0.0,    # 剪切
        'perspective': 0.0,  # 透视
        'flipud': 0.0,   # 上下翻转
        'fliplr': 0.5,   # 左右翻转
        'mosaic': 1.0,   # Mosaic增强
        'mixup': 0.0,    # Mixup增强
    }
    
    # 开始训练
    try:
        results = model.train(**params)
        print(f"训练完成！模型保存在: {results.save_dir}")
        return results
    except Exception as e:
        print(f"训练过程中出现错误: {str(e)}")
        return None

if __name__ == '__main__':
    # 示例使用
    data_yaml = 'data/pedestrian.yaml'  # 数据配置文件路径
    train_model(data_yaml, model_size='n', epochs=100) 