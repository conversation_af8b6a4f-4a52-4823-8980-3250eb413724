<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行人进出计数系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.1.2/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #6a1b9a;
            --secondary-color: #4527a0;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }
        
        body {
            background-color: #f0f2f5;
            font-family: "Microsoft YaHei", sans-serif;
            color: #333;
            overflow-x: hidden;
        }
        
        .main-container {
            padding: 20px;
            margin-top: 70px;
        }
        
        /* 导航栏样式 */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: white !important;
        }
        
        .navbar-brand img {
            height: 40px;
            margin-right: 10px;
        }
        
        .navbar .nav-link {
            color: rgba(255, 255, 255, 0.85) !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            position: relative;
            transition: all 0.3s;
        }
        
        .navbar .nav-link:hover, .navbar .nav-link.active {
            color: white !important;
        }
        
        .navbar .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background-color: white;
            border-radius: 3px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            color: white;
            padding: 0 15px;
        }
        
        .user-info img {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 10px;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
        
        .card {
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border: none;
            margin-bottom: 25px;
            transition: all 0.3s;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            font-weight: bold;
            border: none;
        }
        
        .card-header .card-title {
            margin: 0;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
        }
        
        .card-header .card-title i {
            margin-right: 10px;
            font-size: 1.5rem;
        }
        
        .card-body {
            padding: 25px;
        }
        
        .counter-box {
            text-align: center;
            padding: 20px;
            background-color: rgba(106, 27, 154, 0.05);
            border-radius: 15px;
            transition: all 0.3s;
        }
        
        .counter-box:hover {
            background-color: rgba(106, 27, 154, 0.1);
        }
        
        .counter-value {
            font-size: 3.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .counter-label {
            font-size: 1.1rem;
            color: #666;
            font-weight: 500;
        }
        
        .btn-control {
            margin: 8px;
            border-radius: 50px;
            padding: 12px 25px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }
        
        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-control:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }
        
        #video-feed {
            width: 100%;
            border-radius: 15px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s;
        }
        
        .video-container {
            position: relative;
            overflow: hidden;
            border-radius: 15px;
        }
        
        #video-loading {
            border-radius: 15px;
            background-color: #f8f9fa;
        }
        
        .counter-overlay {
            position: absolute;
            top: 15px;
            left: 0;
            width: 100%;
            padding: 10px 15px;
            z-index: 10;
            display: flex;
            justify-content: space-between;
        }
        
        .in-counter, .out-counter {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
        }
        
        .in-counter i, .out-counter i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        
        .chart-container {
            height: 400px;
            margin-top: 20px;
            margin-bottom: 30px;
            width: 100%;
            border-radius: 15px;
            overflow: hidden;
        }
        
        .nav-tabs {
            border-bottom: none;
            margin-bottom: 20px;
        }
        
        .nav-tabs .nav-link {
            border: none;
            background-color: rgba(106, 27, 154, 0.05);
            color: #666;
            border-radius: 50px;
            padding: 12px 25px;
            margin-right: 10px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .nav-tabs .nav-link:hover {
            background-color: rgba(106, 27, 154, 0.1);
            color: var(--primary-color);
        }
        
        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 5px 15px rgba(106, 27, 154, 0.2);
        }
        
        .form-control, .form-select {
            border-radius: 10px;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            transition: all 0.3s;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(106, 27, 154, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            padding: 15px 20px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
        
        .alert-info {
            background-color: rgba(23, 162, 184, 0.1);
            color: var(--info-color);
            border-left: 4px solid var(--info-color);
        }
        
        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
            border-left: 4px solid var(--success-color);
        }
        
        .alert-warning {
            background-color: rgba(255, 193, 7, 0.1);
            color: var(--warning-color);
            border-left: 4px solid var(--warning-color);
        }
        
        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }
        
        /* 响应式调整 */
        @media (max-width: 992px) {
            .main-container {
                margin-top: 85px;
            }
            .counter-value {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .counter-box {
                margin-bottom: 15px;
            }
            .in-counter, .out-counter {
                padding: 8px 15px;
                font-size: 14px;
            }
            .card-header {
                padding: 15px;
            }
            .card-body {
                padding: 15px;
            }
        }
        
        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: rgba(106, 27, 154, 0.5);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(106, 27, 154, 0.7);
        }
        
        /* 角色权限相关样式 */
        [data-role-access] {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <img src="https://cdn.pixabay.com/photo/2016/11/14/17/39/group-1824145_640.png" alt="Logo" class="d-inline-block align-text-top">
                行人进出计数系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/"><i class="bi bi-camera-video"></i> 实时监控</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/statistics"><i class="bi bi-graph-up"></i> 统计分析</a>
                    </li>
                    <li class="nav-item" data-role-access="admin">
                        <a class="nav-link" href="#settings-modal" data-bs-toggle="modal"><i class="bi bi-gear"></i> 系统设置</a>
                    </li>
                </ul>
                <div class="dropdown">
                    <div class="user-info dropdown-toggle" data-bs-toggle="dropdown">
                        <img src="https://ui-avatars.com/api/?name=User&background=random" alt="用户头像">
                        <div>
                            <div class="user-name" id="username">用户</div>
                            <div class="user-role"><small id="user-role-display">访客</small></div>
                        </div>
                        <i class="bi bi-chevron-down ms-2"></i>
                    </div>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><span class="dropdown-item-text text-muted"><i class="bi bi-person"></i> <span id="dropdown-username">用户</span></span></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#profile-modal" data-bs-toggle="modal"><i class="bi bi-person-badge"></i> 个人信息</a></li>
                        <li><a class="dropdown-item" href="/logout"><i class="bi bi-box-arrow-right"></i> 退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-container container-fluid">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="bi bi-camera-video"></i> 实时监控</h3>
                    </div>
                    <div class="card-body p-0">
                        <!-- 修改这里，添加视频加载状态 -->
                        <div id="video-loading" class="p-5 text-center">
                            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p class="lead">正在加载视频流，请稍候...</p>
                        </div>
                        <div class="video-container">
                            <img id="video-feed" src="/video_feed?t={{ range(1, 100000) | random }}" alt="视频流" 
                                style="max-width: 100%; height: auto; display: none;"
                                onload="this.style.display='block'; document.getElementById('video-loading').style.display='none';"
                                onerror="this.style.display='none'; document.getElementById('video-loading').innerHTML='<div class=\'alert alert-danger m-4\'><i class=\'bi bi-exclamation-triangle\'></i> 视频加载失败，请刷新页面或检查视频源</div>';">
                            <canvas id="drawing-canvas" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: none; cursor: crosshair;"></canvas>
                            <div class="counter-overlay">
                                <div class="in-counter">
                                    <i class="bi bi-box-arrow-in-right"></i> IN: <span id="overlay-in-count">0</span>
                                </div>
                                <div class="out-counter">
                                    <i class="bi bi-box-arrow-right"></i> OUT: <span id="overlay-out-count">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="bi bi-calculator"></i> 计数统计</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="counter-box">
                                    <div id="in-count" class="counter-value">0</div>
                                    <div class="counter-label">进入人数</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="counter-box">
                                    <div id="out-count" class="counter-value">0</div>
                                    <div class="counter-label">离开人数</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button id="start-btn" class="btn btn-success btn-control" data-role-access="admin,operator">
                                <i class="bi bi-play-fill"></i> 开始检测
                            </button>
                            <button id="stop-btn" class="btn btn-danger btn-control" data-role-access="admin,operator">
                                <i class="bi bi-stop-fill"></i> 停止检测
                            </button>
                            <button id="draw-line-btn" class="btn btn-primary btn-control mt-2" data-role-access="admin">
                                <i class="bi bi-pencil"></i> 绘制检测线
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="bi bi-info-circle"></i> 当前状态</h3>
                    </div>
                    <div class="card-body">
                        <div id="status-message" class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i> 系统就绪，请点击"开始检测"按钮
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><i class="bi bi-film"></i> 视频来源</h3>
                    </div>
                    <div class="card-body">
                        <form id="upload-form" enctype="multipart/form-data" data-role-access="admin,operator">
                            <div class="mb-3">
                                <label for="video-file" class="form-label">上传视频文件</label>
                                <input class="form-control" type="file" id="video-file" name="video_file" accept=".mp4,.avi,.mov,.mkv,.webm">
                            </div>
                            <button type="submit" class="btn btn-primary w-100 mb-3">
                                <i class="bi bi-cloud-upload"></i> 上传并使用
                            </button>
                        </form>
                        
                        <div class="mt-3">
                            <label class="form-label">选择已有视频</label>
                            <select id="video-select" class="form-select">
                                <option value="">加载中...</option>
                            </select>
                            <button id="select-video-btn" class="btn btn-secondary w-100 mt-2" data-role-access="admin,operator">
                                <i class="bi bi-check-circle"></i> 使用选中视频
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title"><i class="bi bi-bar-chart"></i> 数据统计</h3>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="statsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="hourly-tab" data-bs-toggle="tab" data-bs-target="#hourly" type="button" role="tab" aria-controls="hourly" aria-selected="true">
                            <i class="bi bi-clock"></i> 小时统计
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily" type="button" role="tab" aria-controls="daily" aria-selected="false">
                            <i class="bi bi-calendar3"></i> 每日统计
                        </button>
                    </li>
                </ul>
                <div class="tab-content" id="statsTabContent">
                    <div class="tab-pane fade show active" id="hourly" role="tabpanel" aria-labelledby="hourly-tab">
                        <div id="hourly-chart" class="chart-container"></div>
                    </div>
                    <div class="tab-pane fade" id="daily" role="tabpanel" aria-labelledby="daily-tab">
                        <div id="daily-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统设置模态框 -->
    <div class="modal fade" id="settings-modal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel"><i class="bi bi-gear"></i> 系统设置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="detection-tab" data-bs-toggle="tab" data-bs-target="#detection-settings" type="button" role="tab" aria-controls="detection-settings" aria-selected="true">
                                <i class="bi bi-camera"></i> 检测设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database-settings" type="button" role="tab" aria-controls="database-settings" aria-selected="false">
                                <i class="bi bi-database"></i> 数据库设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-settings" type="button" role="tab" aria-controls="users-settings" aria-selected="false">
                                <i class="bi bi-people"></i> 用户管理
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content p-3">
                        <div class="tab-pane fade show active" id="detection-settings" role="tabpanel" aria-labelledby="detection-tab">
                            <div class="mb-3">
                                <label for="model-select" class="form-label">检测模型</label>
                                <select class="form-select" id="model-select">
                                    <option value="yolov5n.pt">YOLOv5n (快速)</option>
                                    <option value="yolov5s.pt">YOLOv5s (平衡)</option>
                                    <option value="yolov5m.pt">YOLOv5m (准确)</option>
                                    <option value="yolov5nu.pt" selected>YOLOv5nu (推荐)</option>
                                </select>
                                <div class="form-text">选择合适的模型以平衡检测速度和准确性</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confidence-threshold" class="form-label">置信度阈值: <span id="confidence-value">0.25</span></label>
                                <input type="range" class="form-range" min="0.1" max="0.9" step="0.05" value="0.25" id="confidence-threshold">
                                <div class="form-text">调整检测置信度阈值，较高的阈值可减少误检</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label d-block">设备选择</label>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="device" id="device-cpu" value="cpu" checked>
                                    <label class="form-check-label" for="device-cpu">CPU</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="device" id="device-gpu" value="cuda">
                                    <label class="form-check-label" for="device-gpu">GPU (如可用)</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="database-settings" role="tabpanel" aria-labelledby="database-tab">
                            <div class="mb-3">
                                <label for="db-host" class="form-label">数据库主机</label>
                                <input type="text" class="form-control" id="db-host" value="localhost">
                            </div>
                            
                            <div class="mb-3">
                                <label for="db-user" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="db-user" value="root">
                            </div>
                            
                            <div class="mb-3">
                                <label for="db-password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="db-password" value="root">
                            </div>
                            
                            <div class="mb-3">
                                <label for="db-name" class="form-label">数据库名</label>
                                <input type="text" class="form-control" id="db-name" value="people_count">
                            </div>
                            
                            <button id="test-db-connection" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 测试连接
                            </button>
                            <button id="save-db-settings" class="btn btn-success">
                                <i class="bi bi-save"></i> 保存设置
                            </button>
                        </div>
                        
                        <div class="tab-pane fade" id="users-settings" role="tabpanel" aria-labelledby="users-tab">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th scope="col">#</th>
                                            <th scope="col">用户名</th>
                                            <th scope="col">角色</th>
                                            <th scope="col">最后登录</th>
                                            <th scope="col">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <th scope="row">1</th>
                                            <td>admin</td>
                                            <td><span class="badge bg-primary">管理员</span></td>
                                            <td>2023-11-07 10:30</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil"></i></button>
                                                <button class="btn btn-sm btn-outline-danger" disabled><i class="bi bi-trash"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th scope="row">2</th>
                                            <td>operator</td>
                                            <td><span class="badge bg-success">操作员</span></td>
                                            <td>2023-11-06 15:45</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th scope="row">3</th>
                                            <td>guest</td>
                                            <td><span class="badge bg-secondary">访客</span></td>
                                            <td>2023-11-05 09:20</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary"><i class="bi bi-pencil"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="bi bi-trash"></i></button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <button class="btn btn-success">
                                <i class="bi bi-person-plus"></i> 添加用户
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary">保存更改</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 个人信息模态框 -->
    <div class="modal fade" id="profile-modal" tabindex="-1" aria-labelledby="profileModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileModalLabel"><i class="bi bi-person-badge"></i> 个人信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <img src="https://ui-avatars.com/api/?name=User&background=random" alt="用户头像" class="rounded-circle" style="width: 100px; height: 100px;">
                        <h4 class="mt-2" id="profile-username">用户</h4>
                        <span class="badge bg-primary" id="profile-role">访客</span>
                    </div>
                    
                    <div class="mb-3">
                        <label for="profile-email" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="profile-email" value="<EMAIL>" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="profile-last-login" class="form-label">上次登录时间</label>
                        <input type="text" class="form-control" id="profile-last-login" value="2023-11-07 10:30:00" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="profile-password" class="form-label">修改密码</label>
                        <input type="password" class="form-control" id="profile-password" placeholder="输入新密码">
                    </div>
                    
                    <div class="mb-3">
                        <label for="profile-password-confirm" class="form-label">确认密码</label>
                        <input type="password" class="form-control" id="profile-password-confirm" placeholder="再次输入新密码">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary">保存更改</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 等DOM完全加载后初始化图表和功能
        document.addEventListener('DOMContentLoaded', function() {
            // 根据用户角色显示/隐藏元素
            function applyRoleAccess(role) {
                // 设置用户角色显示
                $('#user-role-display, #profile-role').text(
                    role === 'admin' ? '管理员' : 
                    role === 'operator' ? '操作员' : '访客'
                );
                
                // 根据角色显示/隐藏元素
                $('[data-role-access]').each(function() {
                    const allowedRoles = $(this).data('roleAccess').split(',');
                    if (allowedRoles.includes(role)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
                
                // 保存角色到sessionStorage
                sessionStorage.setItem('user_role', role);
            }
            
            // 从后端渲染的模板变量或sessionStorage获取用户信息
            const username = "{{ username }}" || sessionStorage.getItem('username') || '访客';
            const userRole = "{{ user_role }}" || sessionStorage.getItem('user_role') || 'viewer';
            
            // 设置用户名显示
            $('#username, #dropdown-username, #profile-username').text(username);
            
            // 应用角色权限
            applyRoleAccess(userRole);
            
            // 显示视频加载指示器
            document.getElementById('video-loading').style.display = 'block';
            
            // 设置视频加载检测
            var videoFeed = document.getElementById('video-feed');
            videoFeed.style.display = 'none'; // 初始隐藏视频
            
            // 检查视频流是否成功加载
            function checkVideoLoad() {
                if (videoFeed.complete && videoFeed.naturalWidth > 0) {
                    // 视频流加载成功
                    videoFeed.style.display = 'block';
                    document.getElementById('video-loading').style.display = 'none';
                } else {
                    // 视频流尚未加载成功，继续检查
                    setTimeout(checkVideoLoad, 500);
                }
            }
            
            // 开始检查视频流加载
            checkVideoLoad();
            
            // 全局图表变量
            window.hourlyChart = null;
            window.dailyChart = null;
            
            // 初始化小时图表
            function initHourlyChart() {
                if (window.hourlyChart) {
                    window.hourlyChart.dispose();
                }
                
                var hourlyChartDom = document.getElementById('hourly-chart');
                if (!hourlyChartDom) return;
                
                window.hourlyChart = echarts.init(hourlyChartDom);
                
                // 增强图表样式
                var hourlyOption = {
                    animation: true,
                    title: {
                        text: '今日小时人流量统计',
                        left: 'center',
                        textStyle: {
                            fontSize: 18,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        },
                        confine: true
                    },
                    legend: {
                        data: ['进入人数', '离开人数'],
                        top: 40,
                        textStyle: {
                            fontSize: 14
                        }
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '8%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: ['00:00', '01:00', '02:00', '03:00', '04:00', '05:00', '06:00', 
                            '07:00', '08:00', '09:00', '10:00', '11:00', '12:00',
                            '13:00', '14:00', '15:00', '16:00', '17:00', '18:00',
                            '19:00', '20:00', '21:00', '22:00', '23:00'],
                        axisLabel: {
                            interval: 'auto',
                            rotate: 45,
                            fontSize: 12
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#999'
                            }
                        },
                        axisTick: {
                            alignWithLabel: true
                        }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        minInterval: 1,
                        splitNumber: 5,
                        axisLine: {
                            show: true,
                            lineStyle: {
                                color: '#999'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                color: '#eee'
                            }
                        }
                    },
                    series: [
                        {
                            name: '进入人数',
                            type: 'bar',
                            data: [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                            color: '#4CAF50',
                            barMaxWidth: 30,
                            itemStyle: {
                                borderRadius: [4, 4, 0, 0]
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#2E7D32'
                                }
                            },
                            animationDelay: function (idx) {
                                return idx * 50;
                            }
                        },
                        {
                            name: '离开人数',
                            type: 'bar',
                            data: [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],
                            color: '#F44336',
                            barMaxWidth: 30,
                            itemStyle: {
                                borderRadius: [4, 4, 0, 0]
                            },
                            emphasis: {
                                itemStyle: {
                                    color: '#C62828'
                                }
                            },
                            animationDelay: function (idx) {
                                return idx * 50 + 100;
                            }
                        }
                    ],
                    animationEasing: 'elasticOut',
                    animationDelayUpdate: function (idx) {
                        return idx * 5;
                    }
                };
                
                window.hourlyChart.setOption(hourlyOption);
                
                // 获取最新数据
                updateHourlyData();
            }
            
            // 初始化每日图表
            function initDailyChart() {
                if (window.dailyChart) {
                    window.dailyChart.dispose();
                }
                
                var dailyChartDom = document.getElementById('daily-chart');
                if (!dailyChartDom) return;
                
                dailyChartDom.style.height = '400px';
                dailyChartDom.style.width = '100%';
                dailyChartDom.style.visibility = 'visible';
                
                window.dailyChart = echarts.init(dailyChartDom, null, {
                    renderer: 'canvas',
                    useDirtyRect: false
                });
                
                // 每日统计图表选项
                var dailyOption = {
                    animation: false,
                    title: {
                        text: '最近7天人流量统计'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: ['进入人数', '离开人数'],
                        top: 30
                    },
                    grid: {
                        left: '5%',
                        right: '5%',
                        bottom: '10%',
                        top: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: (function(){
                            const dates = [];
                            const today = new Date();
                            for (let i = 6; i >= 0; i--) {
                                const date = new Date(today);
                                date.setDate(today.getDate() - i);
                                dates.push(date.toISOString().split('T')[0]);
                            }
                            return dates;
                        })(),
                        axisLabel: {
                            interval: 0,
                            rotate: 30,
                            fontSize: 10
                        }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        minInterval: 1,
                        splitNumber: 5
                    },
                    series: [
                        {
                            name: '进入人数',
                            type: 'line',
                            data: [0, 0, 0, 0, 0, 0, 0],
                            color: '#4CAF50',
                            areaStyle: {},
                            emphasis: {
                                focus: 'series'
                            },
                            symbolSize: 8
                        },
                        {
                            name: '离开人数',
                            type: 'line',
                            data: [0, 0, 0, 0, 0, 0, 0],
                            color: '#F44336',
                            areaStyle: {},
                            emphasis: {
                                focus: 'series'
                            },
                            symbolSize: 8
                        }
                    ]
                };
                
                window.dailyChart.setOption(dailyOption);
                window.dailyChart.resize();
                
                // 获取最新数据
                updateDailyData();
            }
            
            // 更新小时数据
            function updateHourlyData() {
                $.getJSON('/hourly_stats', function(data) {
                    if (!data || !data.length) return;
                    
                    var hours = [];
                    var inCounts = [];
                    var outCounts = [];
                    
                    for (var i = 0; i < data.length; i++) {
                        hours.push(data[i].hour + ':00');
                        inCounts.push(data[i].in_count);
                        outCounts.push(data[i].out_count);
                    }
                    
                    const updateOption = {
                        xAxis: { data: hours },
                        series: [
                            { data: inCounts },
                            { data: outCounts }
                        ]
                    };
                    
                    if (window.hourlyChart) {
                        window.hourlyChart.setOption(updateOption);
                        window.hourlyChart.resize();
                    }
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    console.error("获取小时统计数据出错:", textStatus, errorThrown);
                });
            }
            
            // 更新每日数据
            function updateDailyData() {
                $.getJSON('/daily_stats', function(data) {
                    if (!data || !data.length) return;
                    
                    var dates = [];
                    var inCounts = [];
                    var outCounts = [];
                    
                    for (var i = 0; i < data.length; i++) {
                        dates.push(data[i].date);
                        inCounts.push(data[i].in_count);
                        outCounts.push(data[i].out_count);
                    }
                    
                    const updateOption = {
                        xAxis: { data: dates },
                        series: [
                            { data: inCounts },
                            { data: outCounts }
                        ]
                    };
                    
                    if (window.dailyChart) {
                        window.dailyChart.setOption(updateOption);
                        window.dailyChart.resize();
                    }
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    console.error("获取每日统计数据出错:", textStatus, errorThrown);
                });
            }
            
            // 更新计数器
            function updateCounts() {
                $.getJSON('/counts', function(data) {
                    $('#in-count').text(data.in_count);
                    $('#out-count').text(data.out_count);
                    // 更新视频上方的计数显示
                    $('#overlay-in-count').text(data.in_count);
                    $('#overlay-out-count').text(data.out_count);
                }).fail(function(jqXHR, textStatus, errorThrown) {
                    console.error("更新计数出错:", textStatus, errorThrown);
                });
                
                // 每5秒更新一次
                setTimeout(updateCounts, 5000);
            }
            
            // 更新统计数据
            function updateStats() {
                // 更新当前激活的图表
                if ($('#hourly-tab').hasClass('active')) {
                    updateHourlyData();
                }
                if ($('#daily-tab').hasClass('active')) {
                    updateDailyData();
                }
                
                // 每分钟更新一次
                setTimeout(updateStats, 60000);
            }
            
            // 加载可用视频列表
            function loadVideoList() {
                $.getJSON('/videos', function(data) {
                    var select = $('#video-select');
                    select.empty();
                    
                    if (data.length === 0) {
                        select.append($('<option></option>').val('').text('无可用视频'));
                    } else {
                        select.append($('<option></option>').val('').text('请选择视频...'));
                        $.each(data, function(index, video) {
                            select.append($('<option></option>')
                                .val(video.path)
                                .text(video.name));
                        });
                    }
                }).fail(function() {
                    $('#video-select').empty().append($('<option></option>').val('').text('加载失败'));
                });
            }
            
            // 处理视频上传
            $('#upload-form').on('submit', function(e) {
                e.preventDefault();
                
                var fileInput = $('#video-file')[0];
                if (fileInput.files.length === 0) {
                    alert('请选择一个视频文件');
                    return;
                }
                
                var formData = new FormData();
                formData.append('video_file', fileInput.files[0]);
                
                // 显示上传进度
                $('#status-message').removeClass('alert-success alert-danger alert-info')
                    .addClass('alert-warning')
                    .text('正在上传视频，请稍候...');
                
                $.ajax({
                    url: '/upload_video',
                    type: 'POST',
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#status-message').removeClass('alert-warning alert-danger')
                                .addClass('alert-success')
                                .text('视频上传成功: ' + response.message);
                            
                            // 重新加载视频列表
                            loadVideoList();
                            // 清空文件输入
                            $('#video-file').val('');
                        } else {
                            $('#status-message').removeClass('alert-warning alert-success')
                                .addClass('alert-danger')
                                .text('上传失败: ' + response.message);
                        }
                    },
                    error: function() {
                        $('#status-message').removeClass('alert-warning alert-success')
                            .addClass('alert-danger')
                            .text('上传失败: 服务器错误');
                    }
                });
            });
            
            // 处理视频选择
            $('#select-video-btn').on('click', function() {
                var selectedPath = $('#video-select').val();
                if (!selectedPath) {
                    alert('请选择一个视频');
                    return;
                }
                
                $('#status-message').removeClass('alert-success alert-danger alert-info')
                    .addClass('alert-warning')
                    .text('正在切换视频，请稍候...');
                
                $.ajax({
                    url: '/select_video',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({ path: selectedPath }),
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#status-message').removeClass('alert-warning alert-danger')
                                .addClass('alert-success')
                                .text('视频切换成功');
                        } else {
                            $('#status-message').removeClass('alert-warning alert-success')
                                .addClass('alert-danger')
                                .text('切换失败: ' + response.message);
                        }
                    },
                    error: function() {
                        $('#status-message').removeClass('alert-warning alert-success')
                            .addClass('alert-danger')
                            .text('切换失败: 服务器错误');
                    }
                });
            });
            
            // 开始检测
            $('#start-btn').click(function() {
                $('#status-message').removeClass('alert-success alert-danger alert-info')
                    .addClass('alert-warning')
                    .text('正在启动检测，请稍候...');
                
                // 强制重新加载视频源
                var videoFeed = document.getElementById('video-feed');
                videoFeed.style.display = 'none';
                document.getElementById('video-loading').style.display = 'block';
                videoFeed.src = '/video_feed?t=' + Date.now(); // 添加随机参数防止缓存
                
                $.getJSON('/start', function(data) {
                    if (data.status === 'success') {
                        $('#status-message').removeClass('alert-warning alert-danger')
                            .addClass('alert-success')
                            .text('检测已开始，系统正在运行中...');
                        
                        // 确保视频重新加载
                        setTimeout(function() {
                            checkVideoLoad();
                        }, 500);
                    } else if (data.status === 'already_running') {
                        $('#status-message').removeClass('alert-warning alert-danger')
                            .addClass('alert-info')
                            .text('系统已经在运行中');
                    } else {
                        $('#status-message').removeClass('alert-warning alert-success')
                            .addClass('alert-danger')
                            .text('启动失败: ' + data.message);
                    }
                }).fail(function() {
                    $('#status-message').removeClass('alert-warning alert-success')
                        .addClass('alert-danger')
                        .text('启动失败: 服务器错误');
                });
            });
            
            // 停止检测
            $('#stop-btn').click(function() {
                $('#status-message').removeClass('alert-success alert-danger alert-info')
                    .addClass('alert-warning')
                    .text('正在停止检测，请稍候...');
                
                $.getJSON('/stop', function(data) {
                    if (data.status === 'success') {
                        $('#status-message').removeClass('alert-warning alert-danger')
                            .addClass('alert-info')
                            .text('检测已停止，系统就绪');
                    } else {
                        $('#status-message').removeClass('alert-warning alert-info')
                            .addClass('alert-danger')
                            .text('停止失败: ' + data.message);
                    }
                }).fail(function() {
                    $('#status-message').removeClass('alert-warning alert-info')
                        .addClass('alert-danger')
                        .text('停止失败: 服务器错误');
                });
            });
            
            // 切换标签页时重绘图表
            $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
                const targetId = e.target.id;
                
                if (targetId === 'hourly-tab') {
                    setTimeout(function() {
                        if (window.hourlyChart) {
                            window.hourlyChart.resize();
                        } else {
                            initHourlyChart();
                        }
                    }, 50);
                } else if (targetId === 'daily-tab') {
                    setTimeout(function() {
                        if (window.dailyChart) {
                            window.dailyChart.resize();
                        } else {
                            initDailyChart();
                        }
                    }, 50);
                }
            });
            
            // 窗口大小改变时重绘图表
            window.addEventListener('resize', function() {
                if (window.hourlyChart) {
                    window.hourlyChart.resize();
                }
                if (window.dailyChart) {
                    window.dailyChart.resize();
                }
            });
            
            // 添加绘制检测线的功能
            var canvas = document.getElementById('drawing-canvas');
            var ctx = canvas.getContext('2d');
            var videoFeed = document.getElementById('video-feed');
            var isDrawing = false;
            var startPoint = null;
            var endPoint = null;
            
            // 完全重写画布定位和尺寸调整
            function resizeCanvas() {
                // 获取视频容器的位置和尺寸
                const videoContainer = document.querySelector('.video-container');
                const rect = videoFeed.getBoundingClientRect();
                
                // 将画布设置为比视频大一些，并覆盖完整的视频区域
                canvas.style.position = 'absolute';
                canvas.style.top = '0';
                canvas.style.left = '0';
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                canvas.width = rect.width;
                canvas.height = rect.height;
                
                // 如果有已绘制的线，重新绘制
                if (startPoint && endPoint) {
                    drawLine();
                }
            }
            
            // 窗口大小改变时调整画布
            window.addEventListener('resize', resizeCanvas);
            
            // 初始化时调整画布大小
            videoFeed.addEventListener('load', function() {
                setTimeout(resizeCanvas, 100);
            });
            
            // 鼠标按下事件
            canvas.addEventListener('mousedown', function(e) {
                if (!isDrawing) return;
                startPoint = getMousePos(e);
                endPoint = null;
                drawLine();
                
                // 添加鼠标移出画布后释放的处理
                document.addEventListener('mouseup', mouseUpHandler);
            });
            
            // 鼠标移动事件
            canvas.addEventListener('mousemove', function(e) {
                if (!isDrawing || !startPoint) return;
                endPoint = getMousePos(e);
                drawLine();
            });
            
            // 获取鼠标在画布上的坐标
            function getMousePos(evt) {
                const rect = canvas.getBoundingClientRect();
                
                // 计算相对于画布的位置
                const x = evt.clientX - rect.left;
                const y = evt.clientY - rect.top;
                
                // 直接使用原始坐标，不做偏移或限制
                return { x: x, y: y };
            }
            
            // 绘制线
            function drawLine() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                if (startPoint && endPoint) {
                    // 绘制检测线
                    ctx.beginPath();
                    ctx.moveTo(startPoint.x, startPoint.y);
                    ctx.lineTo(endPoint.x, endPoint.y);
                    ctx.strokeStyle = 'rgba(0, 255, 0, 0.8)';
                    ctx.lineWidth = 3;
                    ctx.stroke();
                    
                    // 绘制端点
                    ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                    ctx.beginPath();
                    ctx.arc(startPoint.x, startPoint.y, 5, 0, Math.PI * 2);
                    ctx.arc(endPoint.x, endPoint.y, 5, 0, Math.PI * 2);
                    ctx.fill();
                }
            }
            
            // 通用的鼠标释放事件处理
            function mouseUpHandler(e) {
                if (!isDrawing || !startPoint) return;
                
                // 如果鼠标在canvas内部释放
                if (e.target === canvas) {
                    endPoint = getMousePos(e);
                } 
                // 如果鼠标在canvas外部释放但有临时的终点位置
                else if (endPoint) {
                    // 使用最后一次mousemove记录的endPoint
                } 
                // 如果没有有效的终点，取消操作
                else {
                    document.removeEventListener('mouseup', mouseUpHandler);
                    return;
                }
                
                drawLine();
                
                // 直接使用视频尺寸API获取实际坐标
                $.getJSON('/video_dimensions', function(data) {
                    // 这里使用视频实际尺寸与画布尺寸的比例直接计算
                    const realStartX = Math.round(startPoint.x * (data.width / canvas.width));
                    const realStartY = Math.round(startPoint.y * (data.height / canvas.height));
                    const realEndX = Math.round(endPoint.x * (data.width / canvas.width));
                    const realEndY = Math.round(endPoint.y * (data.height / canvas.height));
                    
                    sendLineCoordinates([realStartX, realStartY], [realEndX, realEndY]);
                }).fail(function() {
                    // 失败时直接使用原始坐标
                    sendLineCoordinates([startPoint.x, startPoint.y], [endPoint.x, endPoint.y]);
                });
                
                // 清除全局mouseup监听
                document.removeEventListener('mouseup', mouseUpHandler);
            }
            
            // 鼠标释放事件（canvas内部）
            canvas.addEventListener('mouseup', mouseUpHandler);
            
            // 发送线段坐标到服务器
            function sendLineCoordinates(startPointCoord, endPointCoord) {
                $.ajax({
                    url: '/set_line',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        start_point: startPointCoord,
                        end_point: endPointCoord
                    }),
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#status-message').removeClass('alert-warning alert-danger')
                                .addClass('alert-success')
                                .text('检测线设置成功');
                        } else {
                            $('#status-message').removeClass('alert-warning alert-success')
                                .addClass('alert-danger')
                                .text('检测线设置失败: ' + response.message);
                        }
                        
                        // 关闭绘制模式
                        toggleDrawMode(false);
                    },
                    error: function() {
                        $('#status-message').removeClass('alert-warning alert-success')
                            .addClass('alert-danger')
                            .text('检测线设置失败: 服务器错误');
                        
                        // 关闭绘制模式
                        toggleDrawMode(false);
                    }
                });
            }
            
            // 切换绘制模式
            function toggleDrawMode(enable) {
                isDrawing = enable;
                canvas.style.display = enable ? 'block' : 'none';
                
                if (enable) {
                    // 清空画布，准备新的绘制
                    startPoint = null;
                    endPoint = null;
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    
                    // 调整画布大小
                    resizeCanvas();
                    
                    $('#status-message').removeClass('alert-success alert-danger alert-info')
                        .addClass('alert-warning')
                        .text('请在视频上拖动鼠标绘制检测线');
                    
                    $('#draw-line-btn').text('取消绘制');
                } else {
                    // 清除可能的全局事件监听
                    document.removeEventListener('mouseup', mouseUpHandler);
                    
                    $('#draw-line-btn').text('绘制检测线');
                }
            }
            
            // 绘制按钮点击事件
            $('#draw-line-btn').click(function() {
                toggleDrawMode(!isDrawing);
            });
            
            // 初始化
            initHourlyChart();
            initDailyChart();
            updateCounts();
            updateStats();
            loadVideoList();
        });
    </script>
</body>
</html> 