<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行人进出计数系统 - 登录</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #6a1b9a, #4527a0);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: "Microsoft YaHei", sans-serif;
        }
        .login-container {
            max-width: 400px;
            width: 90%;
            background-color: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        .login-header {
            text-align: center;
            margin-bottom: 25px;
        }
        .login-header h1 {
            font-size: 24px;
            font-weight: bold;
            color: #6a1b9a;
        }
        .login-header img {
            width: 80px;
            margin-bottom: 15px;
        }
        .form-floating {
            margin-bottom: 20px;
        }
        .form-control:focus {
            border-color: #6a1b9a;
            box-shadow: 0 0 0 0.2rem rgba(106, 27, 154, 0.25);
        }
        .btn-primary {
            background-color: #6a1b9a;
            border-color: #6a1b9a;
            width: 100%;
            padding: 12px;
            font-weight: bold;
            margin-top: 10px;
        }
        .btn-primary:hover, .btn-primary:focus {
            background-color: #4a148c;
            border-color: #4a148c;
        }
        .role-selector {
            margin-bottom: 20px;
        }
        .role-option {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .role-option.selected {
            border-color: #6a1b9a;
            background-color: rgba(106, 27, 154, 0.1);
        }
        .role-option i {
            color: #6a1b9a;
            font-size: 24px;
            margin-right: 10px;
        }
        .role-option .role-name {
            font-weight: bold;
            font-size: 16px;
        }
        .role-option .role-desc {
            color: #6c757d;
            font-size: 13px;
            margin-top: 5px;
        }
        .alert {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <img src="https://cdn.pixabay.com/photo/2016/11/14/17/39/group-1824145_640.png" alt="Logo" class="mb-3">
            <h1>行人进出计数系统</h1>
            <p class="text-muted">请登录以访问系统功能</p>
        </div>
        
        <form id="login-form">
            <div class="role-selector">
                <div class="role-option selected" data-role="admin">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-shield-lock"></i>
                        <div>
                            <div class="role-name">管理员</div>
                            <div class="role-desc">完全访问所有系统功能和设置</div>
                        </div>
                    </div>
                </div>
                <div class="role-option" data-role="operator">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-person-badge"></i>
                        <div>
                            <div class="role-name">操作员</div>
                            <div class="role-desc">可操作系统但无法更改关键设置</div>
                        </div>
                    </div>
                </div>
                <div class="role-option" data-role="viewer">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-eye"></i>
                        <div>
                            <div class="role-name">访客</div>
                            <div class="role-desc">仅可查看数据，无操作权限</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-floating">
                <input type="text" class="form-control" id="username" placeholder="用户名" required>
                <label for="username">用户名</label>
            </div>
            <div class="form-floating">
                <input type="password" class="form-control" id="password" placeholder="密码" required>
                <label for="password">密码</label>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-box-arrow-in-right me-2"></i>登录
            </button>
            
            <div class="alert alert-danger" role="alert" id="error-message">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <span id="error-text">用户名或密码错误</span>
            </div>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            // 角色选择
            $('.role-option').click(function() {
                $('.role-option').removeClass('selected');
                $(this).addClass('selected');
            });
            
            // 登录表单提交
            $('#login-form').submit(function(e) {
                e.preventDefault();
                
                const username = $('#username').val();
                const password = $('#password').val();
                const role = $('.role-option.selected').data('role');
                
                // 显示加载状态
                $('#error-message').hide();
                $('button[type="submit"]').html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>登录中...');
                $('button[type="submit"]').prop('disabled', true);
                
                // 调用后端API进行验证
                $.ajax({
                    url: '/api/login',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        username: username,
                        password: password,
                        role: role
                    }),
                    success: function(response) {
                        if (response.status === 'success') {
                            // 保存用户信息到会话
                            sessionStorage.setItem('user_role', role);
                            sessionStorage.setItem('username', username);
                            
                            // 强制跳转到主页
                            window.location.replace("/");
                        } else {
                            showError(response.message || '登录失败');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '登录失败';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMsg = xhr.responseJSON.message;
                        }
                        showError(errorMsg);
                    }
                });
            });
            
            // 显示错误消息
            function showError(message) {
                $('#error-text').text(message);
                $('#error-message').fadeIn();
                $('button[type="submit"]').html('<i class="bi bi-box-arrow-in-right me-2"></i>登录');
                $('button[type="submit"]').prop('disabled', false);
                
                setTimeout(function() {
                    $('#error-message').fadeOut();
                }, 3000);
            }
            
            // 也支持简单的客户端验证(以防后端API不可用)
            window.clientLogin = function() {
                const username = $('#username').val();
                const password = $('#password').val();
                const role = $('.role-option.selected').data('role');
                
                let isValid = false;
                
                // 简单的登录验证
                if (role === 'admin' && username === 'admin' && password === 'admin123') {
                    isValid = true;
                } else if (role === 'operator' && username === 'operator' && password === 'op123') {
                    isValid = true;
                } else if (role === 'viewer' && username === 'guest' && password === 'guest123') {
                    isValid = true;
                }
                
                if (isValid) {
                    // 保存用户信息到会话
                    sessionStorage.setItem('user_role', role);
                    sessionStorage.setItem('username', username);
                    
                    // 强制跳转到主页 - 使用多种方法确保跳转成功
                    try {
                        window.location.href = "/";
                        setTimeout(function() {
                            window.location.replace("/");
                        }, 100);
                    } catch (e) {
                        console.error("跳转失败:", e);
                        // 如果上面的方法失败，尝试另一种方式
                        document.location.href = "/";
                    }
                    return true;
                } else {
                    showError('用户名或密码错误');
                    return false;
                }
            }
            
            // 添加应急登录方法 - 如果按下回车键
            $(document).keypress(function(e) {
                if (e.which === 13) {
                    // 如果正在等待API响应，使用客户端登录
                    if ($('button[type="submit"]').prop('disabled')) {
                        window.clientLogin();
                    } else {
                        // 如果不是在等待API响应，模拟点击登录按钮
                        $('button[type="submit"]').click();
                    }
                    e.preventDefault();
                    return false;
                }
            });
        });
    </script>
</body>
</html> 