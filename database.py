import datetime
import threading
import logging
import os

# 尝试导入mysql连接器，如果不存在则使用模拟数据库
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    logging.warning("mysql-connector-python 未安装，将使用模拟数据库")

class MockDatabase:
    """当MySQL不可用时使用的模拟数据库类"""
    def __init__(self):
        self.records = []
        self.statistics = {}  # 格式: {(date, hour): (in_count, out_count)}
        self.cursor = self
        logging.info("初始化模拟数据库")
        
    def execute(self, query, params=None):
        """模拟执行SQL查询"""
        self.last_query = query
        self.last_params = params
        return None
        
    def fetchone(self):
        """模拟获取一条结果"""
        if "SUM" in self.last_query:
            # 返回总计数
            total_in = sum(record[2] for record in self.records)
            total_out = sum(record[3] for record in self.records)
            return (total_in, total_out)
        return None
    
    def fetchall(self):
        """模拟获取所有结果"""
        if "statistics WHERE date =" in self.last_query:
            # 返回小时统计
            date = self.last_params[0]
            result = []
            for i in range(24):
                in_count = 0
                out_count = 0
                if (date, i) in self.statistics:
                    in_count, out_count = self.statistics[(date, i)]
                result.append((i, in_count, out_count))
            return result
        
        elif "date BETWEEN" in self.last_query:
            # 返回每日统计
            start_date, end_date = self.last_params
            result = []
            current_date = start_date
            while current_date <= end_date:
                in_count = 0
                out_count = 0
                for hour in range(24):
                    if (current_date, hour) in self.statistics:
                        hour_in, hour_out = self.statistics[(current_date, hour)]
                        in_count += hour_in
                        out_count += hour_out
                result.append((current_date, in_count, out_count))
                current_date += datetime.timedelta(days=1)
            return result
            
        elif "YEARWEEK" in self.last_query:
            # 返回每周统计 (简化版)
            return [(1, 1, 10, 8), (2, 2, 15, 12)]
            
        return []
    
    def commit(self):
        """模拟提交事务"""
        pass
    
    def close(self):
        """模拟关闭连接"""
        pass

class Database:
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(Database, cls).__new__(cls)
                cls._instance._initialized = False
            return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.conn = None
        self.cursor = None
        self._initialized = True
        
        if MYSQL_AVAILABLE:
            try:
                # 尝试连接MySQL数据库
                self.connect()
                # 如果连接成功，创建表
                if self.conn and hasattr(self.conn, 'is_connected') and self.conn.is_connected():
                    self.create_tables()
                else:
                    # 连接失败，使用模拟数据库
                    logging.warning("MySQL连接失败，使用模拟数据库")
                    self.conn = MockDatabase()
                    self.cursor = self.conn
            except Exception as e:
                # 发生异常，使用模拟数据库
                logging.error(f"数据库初始化异常，使用模拟数据库: {e}")
                self.conn = MockDatabase()
                self.cursor = self.conn
        else:
            # 使用模拟数据库
            self.conn = MockDatabase()
            self.cursor = self.conn
            logging.info("使用模拟数据库，所有数据将在程序退出后丢失")
    
    def connect(self):
        """连接到MySQL数据库"""
        if not MYSQL_AVAILABLE:
            return
            
        try:
            self.conn = mysql.connector.connect(
                host="localhost",
                user="root",
                password="123456",  # 使用正确的密码
                database="people_count"
            )
            self.cursor = self.conn.cursor()
            logging.info("数据库连接成功")
        except mysql.connector.Error as err:
            logging.error(f"数据库连接失败: {err}")
            # 如果数据库不存在，则创建数据库
            if hasattr(err, 'errno') and err.errno == mysql.connector.errorcode.ER_BAD_DB_ERROR:
                try:
                    temp_conn = mysql.connector.connect(
                        host="localhost",
                        user="root",
                        password="123456"  # 使用正确的密码
                    )
                    temp_cursor = temp_conn.cursor()
                    temp_cursor.execute("CREATE DATABASE people_count")
                    temp_cursor.close()
                    temp_conn.close()
                    logging.info("数据库创建成功")
                    # 重新连接
                    self.connect()
                except mysql.connector.Error as err:
                    logging.error(f"数据库创建失败: {err}")
    
    def create_tables(self):
        """创建必要的表"""
        if not MYSQL_AVAILABLE:
            return
            
        if not self.conn:
            logging.error("数据库未连接，无法创建表")
            return
            
        try:
            # 创建计数记录表
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS count_records (
                id INT AUTO_INCREMENT PRIMARY KEY,
                timestamp DATETIME NOT NULL,
                in_count INT NOT NULL,
                out_count INT NOT NULL,
                total_count INT NOT NULL,
                video_source VARCHAR(255)
            )
            """)
            
            # 创建统计数据表
            self.cursor.execute("""
            CREATE TABLE IF NOT EXISTS statistics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE NOT NULL,
                hour INT NOT NULL,
                in_count INT NOT NULL,
                out_count INT NOT NULL,
                UNIQUE KEY date_hour (date, hour)
            )
            """)
            
            self.conn.commit()
            logging.info("表创建成功")
        except Exception as err:
            logging.error(f"表创建失败: {err}")
    
    def save_count(self, in_count, out_count, video_source="video.mp4"):
        """保存计数记录"""
        now = datetime.datetime.now()
        total_count = in_count + out_count
        
        if MYSQL_AVAILABLE and self.conn:
            try:
                # 插入计数记录
                self.cursor.execute(
                    "INSERT INTO count_records (timestamp, in_count, out_count, total_count, video_source) VALUES (%s, %s, %s, %s, %s)",
                    (now, in_count, out_count, total_count, video_source)
                )
                
                # 更新统计数据
                self.cursor.execute(
                    """
                    INSERT INTO statistics (date, hour, in_count, out_count) 
                    VALUES (%s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE 
                    in_count = VALUES(in_count),
                    out_count = VALUES(out_count)
                    """,
                    (now.date(), now.hour, in_count, out_count)
                )
                
                self.conn.commit()
                logging.info(f"数据保存成功: in={in_count}, out={out_count}")
                return True
            except Exception as err:
                logging.error(f"数据保存失败: {err}")
                # 尝试重新连接数据库
                try:
                    logging.info("尝试重新连接数据库...")
                    self.connect()
                    if self.conn:
                        logging.info("数据库重新连接成功")
                    else:
                        logging.info("数据库重新连接失败，将使用模拟数据库")
                        self.conn = MockDatabase()
                        self.cursor = self.conn
                except Exception as e:
                    logging.error(f"重连数据库失败: {e}")
                return False
        else:
            # 如果MySQL不可用或连接为None，确保使用模拟数据库
            if self.conn is None:
                self.conn = MockDatabase()
                self.cursor = self.conn
                logging.info("初始化模拟数据库以保存数据")
            
            # 使用模拟数据库存储
            try:
                self.conn.records.append((now, now.date(), in_count, out_count, total_count, video_source))
                
                # 更新模拟统计数据
                key = (now.date(), now.hour)
                self.conn.statistics[key] = (in_count, out_count)
                    
                logging.info(f"数据保存到模拟数据库: in={in_count}, out={out_count}")
                return True
            except Exception as e:
                logging.error(f"保存数据到模拟数据库失败: {e}")
                return False
    
    def get_hourly_statistics(self, date=None):
        """获取指定日期的小时统计数据"""
        if date is None:
            date = datetime.datetime.now().date()
                
        if MYSQL_AVAILABLE and self.conn and hasattr(self.conn, 'is_connected') and self.conn.is_connected():
            try:
                self.cursor.execute(
                    "SELECT hour, in_count, out_count FROM statistics WHERE date = %s ORDER BY hour",
                    (date,)
                )
                
                return self.cursor.fetchall()
            except Exception as err:
                logging.error(f"获取统计数据失败: {err}")
                return []
        else:
            # 确保conn不为None
            if self.conn is None:
                self.conn = MockDatabase()
                self.cursor = self.conn
                logging.info("初始化模拟数据库以查询数据")
                
            # 使用模拟数据库查询
            try:
                # 模拟查询
                self.conn.execute(
                    "SELECT hour, in_count, out_count FROM statistics WHERE date = %s ORDER BY hour",
                    (date,)
                )
                return self.conn.fetchall()
            except Exception as e:
                logging.error(f"从模拟数据库获取统计数据失败: {e}")
                return []
    
    def get_daily_statistics(self, days=7):
        """获取最近几天的每日统计数据"""
        end_date = datetime.datetime.now().date()
        start_date = end_date - datetime.timedelta(days=days-1)
            
        if MYSQL_AVAILABLE and self.conn and hasattr(self.conn, 'is_connected') and self.conn.is_connected():
            try:
                self.cursor.execute(
                    """
                    SELECT date, SUM(in_count) as total_in, SUM(out_count) as total_out 
                    FROM statistics 
                    WHERE date BETWEEN %s AND %s 
                    GROUP BY date
                    ORDER BY date
                    """,
                    (start_date, end_date)
                )
                
                return self.cursor.fetchall()
            except Exception as err:
                logging.error(f"获取每日统计数据失败: {err}")
                return []
        else:
            # 确保conn不为None
            if self.conn is None:
                self.conn = MockDatabase()
                self.cursor = self.conn
                logging.info("初始化模拟数据库以查询数据")
                
            # 使用模拟数据库查询
            try:
                self.conn.execute(
                    """
                    SELECT date, SUM(in_count) as total_in, SUM(out_count) as total_out 
                    FROM statistics 
                    WHERE date BETWEEN %s AND %s 
                    GROUP BY date
                    ORDER BY date
                    """,
                    (start_date, end_date)
                )
                return self.conn.fetchall()
            except Exception as e:
                logging.error(f"从模拟数据库获取每日统计数据失败: {e}")
                return []
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            if MYSQL_AVAILABLE:
                if self.cursor:
                    self.cursor.close()
                self.conn.close()
            logging.info("数据库连接已关闭")
            
    def __del__(self):
        self.close() 