#导入相关依赖

import cv2
import time
import numpy as np
import os
os.environ["KMP_DUPLICATE_LIB_OK"]="TRUE"

from ultralytics import YOLO
import torch
from ultralytics.nn.tasks import DetectionModel
from torch.nn.modules.container import Sequential
from ultralytics.nn.modules import Conv

# 添加安全全局变量
torch.serialization.add_safe_globals([DetectionModel, Sequential, Conv])

#可以使用yolo原始权重，也可以用直接使用onnx格式的权重加速推理
#但请注意目标追踪是建立在目标检测的基础之上,它比单纯的目标检测更耗费算力,用cpu跑很慢,即使是用onnx加速,推理速度也赶不上在gpu上的速度
#推荐电脑有gpu的话，安装gpu的pytorch在gpu上跑,电脑没有gpu的的话，用onnx格式跑
#特别注意:电脑有gpu的话不要安装成了cpu版本的pytroch,否则根本没有用gpu跑，用的还是cpu，速度还是很慢

# 保存原始的 torch.load 函数
original_torch_load = torch.load

# 定义新的加载函数
def custom_load(*args, **kwargs):
    kwargs['weights_only'] = False
    return original_torch_load(*args, **kwargs)

# 替换 torch.load 函数
torch.load = custom_load

# model = YOLO("./yolov5n.onnx") #cpu
model = YOLO("./yolov5n.pt") #gpu

#基本逻辑:在视频中画一条线(不一定是垂直的),线的上下方各有一个区域(视频上不可见，若要设置可见，放开后面注释的代码)
#行人从视频顶部往下部行走并越过线,视为进入;从视频画面下部往上部行走并越过线,视为出去。
#加载要使用的视频
kamera = cv2.VideoCapture("./video.mp4")
font = cv2.FONT_HERSHEY_DUPLEX

#注意线的位置应该根据视频的而定,不是一成不变的,我这里的视频是640*360,我设置线为(0,130)到(640,280)

#注意opencv坐标系是从左上角为原点
# 上面的区域
region1=np.array([(0,130),(0,110),(640,260),(640,280)])
region1 = region1.reshape((-1,1,2))

# 下面的区域
region2=np.array([(0,130),(0,150),(640,300),(640,280)])
region2 = region2.reshape((-1,1,2))

#进入上面(region1)区域的行人id保存在这个变量中
total_ust=set()
# 进入下面(region2)区域的行人id保存到这里
total_alt = set()

# 进 行人的id
first_in = set()

# 出 行人的id
first_out = set()

while True:

    # 逐渐帧读取视频内容
    ret, frame = kamera.read()

    if not ret:
        break
    # opnecv读取的图片色彩空间为BGR,我们把它转化成RGB
    rgb_img = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    # 在屏幕上画线(两区域的平分线)
    cv2.line(frame, (0, 130), (640, 280), (255, 0, 255), 3)
    # 使用yolo8的追踪模式
    results = model.track(rgb_img, persist=True,verbose=False)
    # 遍历yolo的处理结果，结果全部保存在返回的results(list类型)变量中,里面保存着结果信息,可以打印一下看一看(打印results[0])
    for i in range(len(results[0].boxes)):
        #框的四个坐标位置
        x1, y1, x2, y2 = results[0].boxes.xyxy[i]
        # 置信度
        score = results[0].boxes.conf[i]
        # 类别标签label
        cls = results[0].boxes.cls[i]
        # 识别对象的id(同一个物体赋予唯一的id)
        ids = results[0].boxes.id[i]
        #float转化int
        x1, y1, x2, y2, score, cls, ids = int(x1), int(y1), int(x2), int(y2), float(score), int(cls), int(ids)

        # 设置0.2阈值,只有高于这个阈值,后续才会被标记,进入区域才会被计数
        if score < 0.2:
            continue
        # cls=0代表行人,只有行人,后续才会被标记,进入区域才会被计数
        if cls != 0:
            continue

        # 计算行人中心位置的坐标
        cx = int(x1 / 2 + x2 / 2)
        cy = int(y1 / 2 + y2 / 2)

        # 将识别的行人 用框标记出来
        # cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
        # 将识别的行人用圆标记出来
        cv2.circle(frame, (cx, cy), 4, (0, 255, 255), -1)

        # 判断从视频中行人的中心位置与区域1的关系(在内部还是外部)
        inside_region1 = cv2.pointPolygonTest(region1, (cx, cy), False)

        # inside_region1为1表示在区域内部
        if inside_region1 > 0:
            #判断当前行人id是否进入过region2区域
            if ids in total_alt:
                #如果当前行人id进入过region2区域,现在又在region1区域内
                #证明其是从视频下方到视频上方(region2区域->region2区域),视为出去,保存到first_out
                first_out.add(ids)
                # 行人越线,线的颜色改变
                cv2.line(frame, (0, 130), (640, 280), (255, 255, 255), 3)
            #进入region1区域的行人id保存在这个变量中
            total_ust.add(ids)

        inside_region2 = cv2.pointPolygonTest(region2, (cx, cy), False)

        if inside_region2 > 0:
            if ids in total_ust:
                cv2.line(frame, (0, 130), (640, 280), (255, 255, 255), 3)
                first_in.add(ids)
            total_alt.add(ids)
                # 统计进出人数,并设置为字符串,下一步准备在画面上显示
    first_out_str = 'OUT: ' + str(len(first_out))
    first_in_str = 'IN: ' + str(len(first_in))

    # 设置画面左右上角的背景色
    frame[0:40, 0:120] = (102, 0, 153)
    frame[0:40, 510:640] = (102, 0, 153)
    # 将进出行人统计的个数输出到图片,展示
    cv2.putText(frame, first_in_str, (0, 30), font, 1, (255, 255, 255), 1)
    cv2.putText(frame, first_out_str, (510, 30), font, 1, (255, 255, 255), 1)
    # print('IN: ', len(first_in), 'OUT: ', len(first_out))

    # 注意上面说的区域1区域2是逻辑上的划分，但是不可见，如果想在处理结果视频上看到这两个区域，可以放开下面两行注释显示两个区域
    # cv2.polylines(frame, [region1], True, (255, 0, 0), 2)
    # cv2.polylines(frame, [region2], True, (255, 0, 255), 2)

    # 逐帧显示处理后的画面
    cv2.imshow("frame", frame)

    if cv2.waitKey(1) & 0xFF == ord("q"):
        break

kamera.release()
cv2.destroyAllWindows()