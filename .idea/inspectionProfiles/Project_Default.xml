<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="47">
            <item index="0" class="java.lang.String" itemvalue="opencv-python" />
            <item index="1" class="java.lang.String" itemvalue="streamlit" />
            <item index="2" class="java.lang.String" itemvalue="ultralytics" />
            <item index="3" class="java.lang.String" itemvalue="Pillow" />
            <item index="4" class="java.lang.String" itemvalue="transformers" />
            <item index="5" class="java.lang.String" itemvalue="scikit-image" />
            <item index="6" class="java.lang.String" itemvalue="filterpy" />
            <item index="7" class="java.lang.String" itemvalue="hydra-core" />
            <item index="8" class="java.lang.String" itemvalue="cvzone" />
            <item index="9" class="java.lang.String" itemvalue="torch" />
            <item index="10" class="java.lang.String" itemvalue="lap" />
            <item index="11" class="java.lang.String" itemvalue="async-timeout" />
            <item index="12" class="java.lang.String" itemvalue="joblib" />
            <item index="13" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="14" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="15" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="16" class="java.lang.String" itemvalue="PyYAML" />
            <item index="17" class="java.lang.String" itemvalue="datasets" />
            <item index="18" class="java.lang.String" itemvalue="numpy" />
            <item index="19" class="java.lang.String" itemvalue="torchvision" />
            <item index="20" class="java.lang.String" itemvalue="frozenlist" />
            <item index="21" class="java.lang.String" itemvalue="fsspec" />
            <item index="22" class="java.lang.String" itemvalue="filelock" />
            <item index="23" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="24" class="java.lang.String" itemvalue="certifi" />
            <item index="25" class="java.lang.String" itemvalue="multiprocess" />
            <item index="26" class="java.lang.String" itemvalue="urllib3" />
            <item index="27" class="java.lang.String" itemvalue="xxhash" />
            <item index="28" class="java.lang.String" itemvalue="pyarrow" />
            <item index="29" class="java.lang.String" itemvalue="tokenizers" />
            <item index="30" class="java.lang.String" itemvalue="dill" />
            <item index="31" class="java.lang.String" itemvalue="packaging" />
            <item index="32" class="java.lang.String" itemvalue="click" />
            <item index="33" class="java.lang.String" itemvalue="attrs" />
            <item index="34" class="java.lang.String" itemvalue="pandas" />
            <item index="35" class="java.lang.String" itemvalue="tqdm" />
            <item index="36" class="java.lang.String" itemvalue="openai" />
            <item index="37" class="java.lang.String" itemvalue="regex" />
            <item index="38" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="39" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="40" class="java.lang.String" itemvalue="aiohttp" />
            <item index="41" class="java.lang.String" itemvalue="responses" />
            <item index="42" class="java.lang.String" itemvalue="yarl" />
            <item index="43" class="java.lang.String" itemvalue="pytz" />
            <item index="44" class="java.lang.String" itemvalue="python" />
            <item index="45" class="java.lang.String" itemvalue="scipy" />
            <item index="46" class="java.lang.String" itemvalue="requests" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>