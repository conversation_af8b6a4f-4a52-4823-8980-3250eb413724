#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import shutil
from datetime import datetime

def backup_essential_files():
    """
    将项目中最关键的几个代码文件复制到一个新的文件夹中
    """
    # 获取当前日期时间作为备份文件夹名称的一部分
    now = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"essential_backup_{now}"
    
    # 创建备份目录及其子目录结构
    os.makedirs(backup_dir, exist_ok=True)
    os.makedirs(os.path.join(backup_dir, "templates"), exist_ok=True)
    
    # 定义需要备份的关键文件列表
    essential_files = [
        # 核心Python文件
        ("web_app.py", ""),
        ("detector.py", ""),
        ("database.py", ""),
        ("check_database.py", ""),
        ("requirements.txt", ""),
        # HTML模板文件
        ("templates/index.html", "templates"),
        ("templates/login.html", "templates"),
        # 项目说明文件
        ("README.md", "")
    ]
    
    # 复制文件
    for source_file, target_subdir in essential_files:
        if os.path.exists(source_file):
            target_dir = os.path.join(backup_dir, target_subdir)
            target_file = os.path.join(target_dir, os.path.basename(source_file))
            
            print(f"正在备份: {source_file} -> {target_file}")
            shutil.copy2(source_file, target_file)
        else:
            print(f"警告: 文件 {source_file} 不存在，已跳过")
    
    # 尝试复制YOLOv5模型文件(如果存在)
    model_files = ["yolov5n.pt", "yolov5s.pt", "yolov5m.pt", "yolov5l.pt"]
    model_backed_up = False
    
    for model_file in model_files:
        if os.path.exists(model_file):
            print(f"正在备份模型文件: {model_file}")
            shutil.copy2(model_file, os.path.join(backup_dir, model_file))
            model_backed_up = True
            break  # 只需要备份一个模型文件
    
    if not model_backed_up:
        print("警告: 未找到任何YOLOv5模型文件")
    
    # 检查备份是否成功完成
    backup_files = []
    for root, _, files in os.walk(backup_dir):
        for file in files:
            backup_files.append(os.path.join(root, file))
    
    print("\n备份完成!")
    print(f"共成功备份 {len(backup_files)} 个文件到目录: {os.path.abspath(backup_dir)}")
    print("\n备份的文件列表:")
    for file in backup_files:
        print(f" - {file}")

if __name__ == "__main__":
    print("=" * 50)
    print("行人计数系统 - 关键文件备份工具")
    print("=" * 50)
    print("此工具将备份系统最关键的几个文件到一个新文件夹中")
    print("=" * 50)
    
    try:
        backup_essential_files()
    except Exception as e:
        print(f"\n备份过程中出现错误: {e}") 