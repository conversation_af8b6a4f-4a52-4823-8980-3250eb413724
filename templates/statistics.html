<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人流量统计分析 - 行人进出计数系统</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.1.2/echarts.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/moment.js/2.29.1/locale/zh-cn.js"></script>
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "Microsoft YaHei", sans-serif;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            background-color: white;
        }
        .card-header {
            background-color: #6a1b9a;
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 15px;
            font-weight: bold;
        }
        .chart-container {
            height: 400px;
            margin-top: 20px;
        }
        .summary-box {
            background-color: #f7f7f7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            text-align: center;
        }
        .summary-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #28a745;
        }
        .summary-label {
            font-size: 1rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            background-color: #f1f1f1;
            border-color: #dee2e6 #dee2e6 #f1f1f1;
        }
        .tab-content {
            background-color: #f1f1f1;
            border-radius: 0 0 10px 10px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark rounded mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">行人进出计数系统</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">首页</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/statistics">统计分析</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <h1 class="text-center my-4">人流量统计分析</h1>
        
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="summary-box">
                    <div id="total-in" class="summary-value">0</div>
                    <div class="summary-label">累计进入人数</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-box">
                    <div id="total-out" class="summary-value">0</div>
                    <div class="summary-label">累计离开人数</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="summary-box">
                    <div id="total-flow" class="summary-value">0</div>
                    <div class="summary-label">总人流量</div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h3 class="card-title m-0">时间段分析</h3>
                <div class="form-inline">
                    <select id="date-selector" class="form-select">
                        <!-- 日期选项会通过JavaScript动态生成 -->
                    </select>
                </div>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="timeTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="hourly-tab" data-bs-toggle="tab" data-bs-target="#hourly" type="button" role="tab" aria-controls="hourly" aria-selected="true">小时统计</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily" type="button" role="tab" aria-controls="daily" aria-selected="false">每日统计</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="weekly-tab" data-bs-toggle="tab" data-bs-target="#weekly" type="button" role="tab" aria-controls="weekly" aria-selected="false">每周统计</button>
                    </li>
                </ul>
                <div class="tab-content" id="timeTabContent">
                    <div class="tab-pane fade show active" id="hourly" role="tabpanel" aria-labelledby="hourly-tab">
                        <div id="hourly-chart" class="chart-container"></div>
                    </div>
                    <div class="tab-pane fade" id="daily" role="tabpanel" aria-labelledby="daily-tab">
                        <div id="daily-chart" class="chart-container"></div>
                    </div>
                    <div class="tab-pane fade" id="weekly" role="tabpanel" aria-labelledby="weekly-tab">
                        <div id="weekly-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title m-0">流量对比分析</h3>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="comparisonTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="in-out-tab" data-bs-toggle="tab" data-bs-target="#in-out" type="button" role="tab" aria-controls="in-out" aria-selected="true">进出对比</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="trend-tab" data-bs-toggle="tab" data-bs-target="#trend" type="button" role="tab" aria-controls="trend" aria-selected="false">趋势分析</button>
                    </li>
                </ul>
                <div class="tab-content" id="comparisonTabContent">
                    <div class="tab-pane fade show active" id="in-out" role="tabpanel" aria-labelledby="in-out-tab">
                        <div id="in-out-chart" class="chart-container"></div>
                    </div>
                    <div class="tab-pane fade" id="trend" role="tabpanel" aria-labelledby="trend-tab">
                        <div id="trend-chart" class="chart-container"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        var hourlyChart = echarts.init(document.getElementById('hourly-chart'));
        var dailyChart = echarts.init(document.getElementById('daily-chart'));
        var weeklyChart = echarts.init(document.getElementById('weekly-chart'));
        var inOutChart = echarts.init(document.getElementById('in-out-chart'));
        var trendChart = echarts.init(document.getElementById('trend-chart'));
        
        // 小时统计图表选项
        var hourlyOption = {
            title: {
                text: '小时人流量统计'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['进入人数', '离开人数']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '进入人数',
                    type: 'bar',
                    data: [],
                    color: '#4CAF50'
                },
                {
                    name: '离开人数',
                    type: 'bar',
                    data: [],
                    color: '#F44336'
                }
            ]
        };
        
        // 每日统计图表选项
        var dailyOption = {
            title: {
                text: '每日人流量统计'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['进入人数', '离开人数']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '进入人数',
                    type: 'line',
                    data: [],
                    color: '#4CAF50',
                    areaStyle: {}
                },
                {
                    name: '离开人数',
                    type: 'line',
                    data: [],
                    color: '#F44336',
                    areaStyle: {}
                }
            ]
        };
        
        // 每周统计图表选项
        var weeklyOption = {
            title: {
                text: '每周人流量统计'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['进入人数', '离开人数']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '进入人数',
                    type: 'bar',
                    data: [],
                    color: '#4CAF50'
                },
                {
                    name: '离开人数',
                    type: 'bar',
                    data: [],
                    color: '#F44336'
                }
            ]
        };
        
        // 进出对比图表选项
        var inOutOption = {
            title: {
                text: '进出人数对比'
            },
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                left: 'left'
            },
            series: [
                {
                    name: '人流量',
                    type: 'pie',
                    radius: '50%',
                    data: [
                        { value: 0, name: '进入人数', itemStyle: { color: '#4CAF50' } },
                        { value: 0, name: '离开人数', itemStyle: { color: '#F44336' } }
                    ],
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        
        // 趋势分析图表选项
        var trendOption = {
            title: {
                text: '人流量趋势分析'
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['进入人数', '离开人数', '净增长']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: []
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '进入人数',
                    type: 'line',
                    data: [],
                    color: '#4CAF50'
                },
                {
                    name: '离开人数',
                    type: 'line',
                    data: [],
                    color: '#F44336'
                },
                {
                    name: '净增长',
                    type: 'line',
                    data: [],
                    color: '#2196F3',
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            ]
        };
        
        // 设置图表选项
        hourlyChart.setOption(hourlyOption);
        dailyChart.setOption(dailyOption);
        weeklyChart.setOption(weeklyOption);
        inOutChart.setOption(inOutOption);
        trendChart.setOption(trendOption);
        
        // 调整窗口大小时重绘图表
        window.addEventListener('resize', function() {
            hourlyChart.resize();
            dailyChart.resize();
            weeklyChart.resize();
            inOutChart.resize();
            trendChart.resize();
        });
        
        $(document).ready(function() {
            // 生成日期选择器选项
            function generateDateOptions() {
                const dateSelector = $('#date-selector');
                const today = moment();
                
                // 清空现有选项
                dateSelector.empty();
                
                // 添加过去7天的选项
                for (let i = 0; i < 7; i++) {
                    const date = moment().subtract(i, 'days');
                    const dateText = date.format('YYYY-MM-DD') + (i === 0 ? ' (今天)' : '');
                    dateSelector.append(`<option value="${date.format('YYYY-MM-DD')}">${dateText}</option>`);
                }
            }
            
            // 更新统计数据
            function updateStatistics() {
                const selectedDate = $('#date-selector').val();
                
                // 获取摘要数据
                $.getJSON('/api/summary', function(data) {
                    $('#total-in').text(data.total_in);
                    $('#total-out').text(data.total_out);
                    $('#total-flow').text(data.total_in + data.total_out);
                    
                    // 更新进出对比图表
                    inOutOption.series[0].data[0].value = data.total_in;
                    inOutOption.series[0].data[1].value = data.total_out;
                    inOutChart.setOption(inOutOption);
                });
                
                // 获取小时统计数据
                $.getJSON(`/api/hourly_stats?date=${selectedDate}`, function(data) {
                    const hours = [];
                    const inCounts = [];
                    const outCounts = [];
                    
                    for (let i = 0; i < data.length; i++) {
                        hours.push(data[i].hour + ':00');
                        inCounts.push(data[i].in_count);
                        outCounts.push(data[i].out_count);
                    }
                    
                    hourlyOption.xAxis.data = hours;
                    hourlyOption.series[0].data = inCounts;
                    hourlyOption.series[1].data = outCounts;
                    hourlyChart.setOption(hourlyOption);
                });
                
                // 获取每日统计数据
                $.getJSON('/api/daily_stats', function(data) {
                    const dates = [];
                    const inCounts = [];
                    const outCounts = [];
                    const netChanges = [];
                    
                    for (let i = 0; i < data.length; i++) {
                        dates.push(data[i].date);
                        inCounts.push(data[i].in_count);
                        outCounts.push(data[i].out_count);
                        netChanges.push(data[i].in_count - data[i].out_count);
                    }
                    
                    // 更新每日统计图表
                    dailyOption.xAxis.data = dates;
                    dailyOption.series[0].data = inCounts;
                    dailyOption.series[1].data = outCounts;
                    dailyChart.setOption(dailyOption);
                    
                    // 更新趋势分析图表
                    trendOption.xAxis.data = dates;
                    trendOption.series[0].data = inCounts;
                    trendOption.series[1].data = outCounts;
                    trendOption.series[2].data = netChanges;
                    trendChart.setOption(trendOption);
                });
                
                // 获取每周统计数据
                $.getJSON('/api/weekly_stats', function(data) {
                    const weeks = [];
                    const inCounts = [];
                    const outCounts = [];
                    
                    for (let i = 0; i < data.length; i++) {
                        weeks.push('第' + data[i].week + '周');
                        inCounts.push(data[i].in_count);
                        outCounts.push(data[i].out_count);
                    }
                    
                    weeklyOption.xAxis.data = weeks;
                    weeklyOption.series[0].data = inCounts;
                    weeklyOption.series[1].data = outCounts;
                    weeklyChart.setOption(weeklyOption);
                });
            }
            
            // 日期变更时更新数据
            $('#date-selector').change(function() {
                updateStatistics();
            });
            
            // 初始化页面
            generateDateOptions();
            updateStatistics();
            
            // 每分钟自动刷新数据
            setInterval(updateStatistics, 60000);
        });
    </script>
</body>
</html> 