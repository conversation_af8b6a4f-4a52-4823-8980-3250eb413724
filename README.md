# 基于YOLOv5的行人进出双向计数系统

本项目利用YOLOv5目标检测模型实现了实时行人检测、跟踪和双向计数功能，并提供了Web界面用于实时监控展示与数据统计分析。系统可以精确统计穿过检测线的行人数量，区分进入和离开方向，并将数据持久化存储到MySQL数据库中。

## 功能特点

- **实时行人检测与跟踪**：使用YOLOv5模型精确识别视频中的行人，支持多种模型精度选择
- **双向计数**：自动统计行人进入和离开指定区域的人数，通过自定义检测线实现
- **Web界面**：现代化的网页界面，实时展示监控视频和计数结果，支持响应式设计
- **用户权限管理**：支持不同权限用户（管理员、操作员、访客）的登录及功能控制
- **自定义检测线**：支持通过Web界面直接绘制自定义检测线，适应不同场景需求
- **数据统计分析**：提供小时、每日、每周的人流量统计图表，直观展示人流趋势
- **数据库存储**：使用MySQL数据库持久化存储计数数据，支持长期数据保存和查询
- **视频源管理**：支持上传、选择不同视频源进行检测，灵活应对不同监控场景

## 系统架构

系统由以下主要模块组成：

1. **检测模块（detector.py）**：使用YOLOv5模型进行行人目标检测和ID跟踪
2. **计数模块（detector.py）**：基于行人穿过检测线的方向进行双向计数
3. **Web服务模块（web_app.py）**：基于Flask的Web服务，提供用户界面和API接口
4. **数据存储模块（database.py）**：负责数据持久化存储和统计数据查询
5. **用户认证模块**：基于会话的用户登录和权限管理系统
6. **前端界面（templates/）**：基于Bootstrap和ECharts的响应式Web界面

## 环境要求

- **Python 3.8+**
- **MySQL 5.7+**
- **CUDA和兼容显卡** (可选，用于GPU加速)
- **现代Web浏览器** (Chrome, Firefox, Edge等)

## 安装步骤

### 1. 安装Python环境

确保已安装Python 3.8或更高版本。

### 2. 克隆仓库

```bash
git clone https://github.com/your-username/people-counter-yolov5.git
cd people-counter-yolov5
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

如果需要GPU加速（建议），请根据您的CUDA版本安装对应的PyTorch版本。例如，对于CUDA 11.8:

```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
pip install -r requirements.txt
```

### 4. 下载YOLOv5模型

项目已包含基础模型(yolov5n.pt)，但您也可以下载其他版本的模型：

```bash
# 可选：下载不同精度的模型
# YOLOv5n (速度最快，精度较低)
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5n.pt

# YOLOv5s (速度和精度平衡)
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5s.pt

# YOLOv5m (中等精度)
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5m.pt

# YOLOv5l (高精度，速度较慢)
wget https://github.com/ultralytics/yolov5/releases/download/v7.0/yolov5l.pt
```

### 5. 配置数据库

安装MySQL数据库，然后执行以下命令验证数据库连接并创建必要的表结构：

```bash
python check_database.py
```

如果遇到数据库连接问题，请修改 `database.py` 和 `check_database.py` 中的连接参数：

```python
# 修改以下参数为您的MySQL配置
host="localhost"
user="root"
password="root"  # 修改为您的实际密码
database="people_count"
```

### 6. 运行应用

```bash
python web_app.py
```

应用将在 `http://localhost:5000` 上启动。

## 使用说明

### 用户登录

系统支持三种用户角色，各有不同的权限：

- **管理员 (admin)**：用户名 `admin`，密码 `admin123`，可访问所有功能
- **操作员 (operator)**：用户名 `operator`，密码 `op123`，可操作系统但无法更改关键设置
- **访客 (viewer)**：用户名 `guest`，密码 `guest123`，仅可查看数据，无操作权限

### 基本操作

1. **开始/停止检测**：
   - 点击界面中的"开始检测"按钮开始行人检测和计数
   - 点击"停止检测"可暂停检测过程

2. **自定义检测线**：
   - 点击"绘制检测线"按钮，然后在视频上拖动鼠标绘制检测线
   - 行人穿过此线时将根据方向进行计数

3. **切换视频源**：
   - 通过"上传视频文件"上传新的视频源
   - 或从"选择已有视频"下拉列表中选择视频

4. **查看统计数据**：
   - 主页下方提供小时统计和每日统计图表
   - 点击"统计分析"可查看更详细的数据统计

5. **系统设置**：
   - 管理员可以通过系统设置修改检测模型、置信度阈值、数据库配置等
   - 管理员可以在用户管理中添加、编辑或删除用户

### 监控界面

监控界面展示实时监控视频和计数结果，包括：

- 实时视频流显示，附带检测框、ID标记和检测线
- 实时计数器显示进入和离开的人数
- 操作控制按钮（开始/停止检测、绘制检测线等）
- 数据统计图表（小时统计、每日统计）

## 项目结构

```
├── web_app.py           # Web应用主程序和API接口
├── detector.py          # 行人检测和计数模块
├── database.py          # 数据库操作模块
├── check_database.py    # 数据库检查工具
├── setup_database.py    # 数据库初始化工具
├── requirements.txt     # 项目依赖
├── yolov5n.pt           # YOLOv5n模型文件
├── templates/           # HTML模板文件
│   ├── index.html       # 主页模板
│   ├── login.html       # 登录页模板
│   └── statistics.html  # 统计页面模板
├── static/              # 静态资源文件
├── uploads/             # 用户上传的视频文件
└── README.md            # 项目说明
```

## 自定义配置

### 修改检测模型

在系统设置中选择不同的YOLOv5模型，或修改 `web_app.py` 中的模型路径：

```python
detector = PeopleDetector('yolov5n.pt')  # 改为其他模型，如'yolov5s.pt'
```

### 修改检测线位置

通过Web界面的"绘制检测线"功能可视化调整，或修改 `web_app.py` 中的 `line_points` 参数：

```python
line_points = ((0, int(height * 0.5)), (width, int(height * 0.5)))  # 水平中线
```

### 调整检测灵敏度

在系统设置中调整置信度阈值，或修改 `detector.py` 中的阈值设置：

```python
if score < 0.25:  # 修改为更适合您场景的阈值
    continue
```

### 自定义数据库配置

修改 `database.py` 中的数据库连接参数：

```python
self.conn = mysql.connector.connect(
    host="localhost",      # 数据库服务器地址
    user="root",           # 数据库用户名
    password="root",       # 数据库密码
    database="people_count"  # 数据库名称
)
```

## 技术栈

- **后端**：Python, Flask, PyTorch, YOLOv5, OpenCV
- **前端**：HTML, CSS, JavaScript, Bootstrap 5, ECharts
- **数据库**：MySQL
- **部署**：支持Docker容器化部署(可选)

## 常见问题解答

### Q: 系统显示"无法连接到数据库"怎么办？

A: 检查MySQL服务是否运行，并确认 `database.py` 中的数据库连接参数是否正确。您也可以运行 `check_database.py` 来诊断和修复数据库问题。

### Q: 如何提高检测精度？

A: 可以使用更高精度的YOLOv5模型(如yolov5m.pt或yolov5l.pt)，或调整检测阈值。在系统设置中可以方便地进行这些调整。

### Q: 系统在高分辨率视频上运行缓慢，如何优化？

A: 建议使用支持CUDA的GPU加速，或降低视频分辨率。如果仍有性能问题，可以使用更轻量的模型(如yolov5n.pt)或降低帧率。

## 许可证

本项目基于MIT许可证开源，详情请参阅LICENSE文件。

## 致谢

- YOLOv5: https://github.com/ultralytics/yolov5
- Flask: https://flask.palletsprojects.com/
- Bootstrap: https://getbootstrap.com/
- ECharts: https://echarts.apache.org/ 