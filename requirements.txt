#python版本不强制要求，但最好为3.10

#cpu环境(若虚拟环境中无pytorch,ultralytics底层自动依赖下载cpu版本的torch,如对项目的检测速度有要求请安装gpu版本的torch)
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0
opencv-python>=4.8.0
numpy>=1.24.0
flask>=2.0.0
flask-cors>=4.0.0
python-dotenv>=1.0.0
onnx>=1.12.0
onnxruntime-gpu>=1.15.0
pillow>=10.0.0
tqdm>=4.65.0

#gpu环境(这里安装的是gpu版本的torch,请到pytorch官网复制与你的英伟达显卡驱动所支持的cuda版本对应的安装命令,下面以cuda 11.8为例给出示例命令)
# pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118 （先在环境中下载好gpu版本的torch，再安装ultralytics）
# pip3 install ultralytics==8.0.170

# 基础依赖
numpy>=1.20.0
opencv-python>=4.5.0
torch>=2.0.0
torchvision>=0.15.0

# YOLO模型
ultralytics>=8.0.0

# Web应用
flask>=2.0.0
flask-cors>=3.0.10

# 数据库
mysql-connector-python>=8.0.27

# 其他工具
tqdm>=4.64.0
pandas>=1.3.0
matplotlib>=3.5.0
seaborn>=0.11.0