Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple
Requirement already satisfied: onnx in d:\anaconda\envs\count-people-yolo8\lib\site-packages (1.17.0)
Requirement already satisfied: numpy>=1.20 in d:\anaconda\envs\count-people-yolo8\lib\site-packages (from onnx) (2.2.4)
Requirement already satisfied: protobuf>=3.20.2 in d:\anaconda\envs\count-people-yolo8\lib\site-packages (from onnx) (6.30.2)
