"""
检查数据库连接并创建所需的数据库结构
"""

import mysql.connector
import logging
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_database():
    """检查数据库连接并创建必要的表结构"""
    try:
        # 连接MySQL服务器
        logging.info("尝试连接到MySQL服务器")
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        cursor = conn.cursor()
        
        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE 'people_count'")
        exists = cursor.fetchone()
        
        if not exists:
            # 创建数据库
            logging.info("创建数据库 people_count")
            cursor.execute("CREATE DATABASE people_count")
        else:
            logging.info("数据库 people_count 已存在")
        
        # 切换到数据库
        cursor.execute("USE people_count")
        
        # 创建计数记录表
        logging.info("创建表 count_records")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS count_records (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            in_count INT NOT NULL,
            out_count INT NOT NULL,
            total_count INT NOT NULL,
            video_source VARCHAR(255)
        )
        """)
        
        # 创建统计数据表
        logging.info("创建表 statistics")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS statistics (
            id INT AUTO_INCREMENT PRIMARY KEY,
            date DATE NOT NULL,
            hour INT NOT NULL,
            in_count INT NOT NULL,
            out_count INT NOT NULL,
            UNIQUE KEY date_hour (date, hour)
        )
        """)
        
        conn.commit()
        logging.info("数据库检查完成，所有表结构已创建")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
    except mysql.connector.Error as err:
        logging.error(f"数据库操作失败: {err}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("行人计数系统 - 数据库检查工具")
    print("=" * 60)
    
    if check_database():
        print("数据库配置正确！系统已准备就绪。")
        print("你可以现在运行主程序: python web_app.py")
    else:
        print("数据库配置有问题，请检查错误信息并修复")
        sys.exit(1) 